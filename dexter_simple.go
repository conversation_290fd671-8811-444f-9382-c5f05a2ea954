package main

import (
	"fmt"
	"math"

	rl "github.com/gen2brain/raylib-go/raylib"
)

const (
	screenWidth  = int32(800)
	screenHeight = int32(600)
	gameTitle    = "Dexter: Dark Passenger - 3D Investigation Game"
)

// Player represents the player character (<PERSON>)
type Player struct {
	position rl.Vector3
	speed    float32
}

// Evidence represents collectible evidence
type Evidence struct {
	position    rl.Vector3
	collected   bool
	description string
}

// NPC represents non-player characters
type NPC struct {
	position        rl.Vector3
	detectionRadius float32
	isAlerted       bool
}

// Game represents the main game state
type Game struct {
	camera   rl.Camera3D
	player   Player
	evidence []Evidence
	npcs     []NPC
	gameStarted bool
}

// NewGame creates a new game instance
func NewGame() *Game {
	game := &Game{
		player: Player{
			position: rl.Vector3{X: 0, Y: 1, Z: 0},
			speed:    0.1,
		},
		gameStarted: false,
	}

	// Initialize camera
	game.camera = rl.Camera3D{
		Position:   rl.Vector3{X: 10, Y: 10, Z: 10},
		Target:     rl.Vector3{X: 0, Y: 0, Z: 0},
		Up:         rl.Vector3{X: 0, Y: 1, Z: 0},
		Fovy:       45.0,
		Projection: rl.CameraPerspective,
	}

	// Initialize evidence
	game.evidence = []Evidence{
		{position: rl.Vector3{X: 5, Y: 1, Z: 5}, description: "Blood sample"},
		{position: rl.Vector3{X: -5, Y: 1, Z: -5}, description: "Knife"},
		{position: rl.Vector3{X: 0, Y: 1, Z: 8}, description: "Photo"},
	}

	// Initialize NPCs
	game.npcs = []NPC{
		{position: rl.Vector3{X: -10, Y: 1, Z: -10}, detectionRadius: 5.0},
		{position: rl.Vector3{X: 10, Y: 1, Z: 10}, detectionRadius: 6.0},
	}

	return game
}

// Update handles game logic
func (g *Game) Update() {
	if !g.gameStarted {
		if rl.IsKeyPressed(rl.KeyEnter) {
			g.gameStarted = true
		}
		return
	}

	// Player movement
	if rl.IsKeyDown(rl.KeyW) {
		g.player.position.Z -= g.player.speed
	}
	if rl.IsKeyDown(rl.KeyS) {
		g.player.position.Z += g.player.speed
	}
	if rl.IsKeyDown(rl.KeyA) {
		g.player.position.X -= g.player.speed
	}
	if rl.IsKeyDown(rl.KeyD) {
		g.player.position.X += g.player.speed
	}

	// Update camera to follow player
	g.camera.Position = rl.Vector3{
		X: g.player.position.X + 8,
		Y: g.player.position.Y + 8,
		Z: g.player.position.Z + 8,
	}
	g.camera.Target = g.player.position

	// Check evidence collection
	for i := range g.evidence {
		if !g.evidence[i].collected {
			distance := rl.Vector3Distance(g.evidence[i].position, g.player.position)
			if distance < 2.0 && rl.IsKeyPressed(rl.KeyE) {
				g.evidence[i].collected = true
				fmt.Printf("Collected: %s\n", g.evidence[i].description)
			}
		}
	}

	// Update NPC detection
	for i := range g.npcs {
		distance := rl.Vector3Distance(g.npcs[i].position, g.player.position)
		g.npcs[i].isAlerted = distance < g.npcs[i].detectionRadius
	}
}

// Render handles all rendering
func (g *Game) Render() {
	rl.BeginDrawing()
	rl.ClearBackground(rl.Black)

	if !g.gameStarted {
		// Main menu
		rl.DrawText("DEXTER: DARK PASSENGER", screenWidth/2-200, screenHeight/2-100, 40, rl.Red)
		rl.DrawText("3D Investigation Game", screenWidth/2-120, screenHeight/2-50, 20, rl.White)
		rl.DrawText("Press ENTER to Start", screenWidth/2-100, screenHeight/2+20, 20, rl.Gray)
		rl.DrawText("WASD to Move, E to Collect Evidence", screenWidth/2-150, screenHeight/2+60, 16, rl.LightGray)
	} else {
		// 3D Game world
		rl.BeginMode3D(g.camera)

		// Draw ground
		rl.DrawPlane(rl.Vector3{X: 0, Y: 0, Z: 0}, rl.Vector2{X: 50, Y: 50}, rl.DarkGreen)

		// Draw some buildings
		rl.DrawCube(rl.Vector3{X: -15, Y: 3, Z: -15}, 6, 6, 6, rl.Gray)
		rl.DrawCube(rl.Vector3{X: 15, Y: 4, Z: 15}, 8, 8, 8, rl.DarkGray)
		rl.DrawCube(rl.Vector3{X: -15, Y: 2, Z: 15}, 4, 4, 4, rl.LightGray)

		// Draw player (Dexter)
		rl.DrawCube(g.player.position, 1, 2, 1, rl.Blue)
		rl.DrawCubeWires(g.player.position, 1, 2, 1, rl.DarkBlue)

		// Draw NPCs
		for _, npc := range g.npcs {
			color := rl.Orange
			if npc.isAlerted {
				color = rl.Red
			}
			rl.DrawCube(npc.position, 1, 2, 1, color)
			
			// Draw detection radius (semi-transparent)
			rl.DrawCircle3D(npc.position, npc.detectionRadius, rl.Vector3{X: 1, Y: 0, Z: 0}, 90, rl.Fade(rl.Red, 0.1))
		}

		// Draw evidence
		for _, evidence := range g.evidence {
			if !evidence.collected {
				// Animate evidence with floating effect
				animY := evidence.position.Y + float32(math.Sin(float64(rl.GetTime()*3)))*0.2
				pos := rl.Vector3{X: evidence.position.X, Y: animY, Z: evidence.position.Z}
				rl.DrawCube(pos, 0.5, 0.5, 0.5, rl.Gold)
				rl.DrawCubeWires(pos, 0.5, 0.5, 0.5, rl.Yellow)
			}
		}

		rl.EndMode3D()

		// HUD
		collectedCount := 0
		for _, evidence := range g.evidence {
			if evidence.collected {
				collectedCount++
			}
		}
		
		rl.DrawText(fmt.Sprintf("Evidence: %d/%d", collectedCount, len(g.evidence)), 10, 10, 20, rl.White)
		
		// Detection status
		detected := false
		for _, npc := range g.npcs {
			if npc.isAlerted {
				detected = true
				break
			}
		}
		
		if detected {
			rl.DrawText("DETECTED!", screenWidth-120, 10, 20, rl.Red)
		} else {
			rl.DrawText("HIDDEN", screenWidth-80, 10, 20, rl.Green)
		}
		
		rl.DrawText("WASD: Move | E: Collect | ESC: Exit", 10, screenHeight-30, 16, rl.LightGray)
	}

	rl.EndDrawing()
}

func main() {
	// Initialize raylib
	rl.InitWindow(screenWidth, screenHeight, gameTitle)
	defer rl.CloseWindow()
	rl.SetTargetFPS(60)

	// Create game instance
	game := NewGame()

	fmt.Println("Starting Dexter: Dark Passenger 3D Game...")
	fmt.Println("Use WASD to move, E to collect evidence")
	fmt.Println("Avoid the red NPCs - they will detect you!")

	// Main game loop
	for !rl.WindowShouldClose() {
		game.Update()
		game.Render()
		
		// Exit on ESC
		if rl.IsKeyPressed(rl.KeyEscape) && game.gameStarted {
			break
		}
	}

	fmt.Println("Game ended. Thanks for playing!")
}
