package main

import (
	"fmt"
	"math"

	rl "github.com/gen2brain/raylib-go/raylib"
)

const (
	screenWidth  = int32(1024)
	screenHeight = int32(768)
	gameTitle    = "Dexter: Dark Passenger - 3D Investigation Game"
)

// Game represents the main game state
type Game struct {
	camera      rl.Camera3D
	playerPos   rl.Vector3
	gameState   GameState
	evidence    []Evidence
	npcs        []NPC
	environment Environment
}

// GameState represents different states of the game
type GameState int

const (
	GameStateMenu GameState = iota
	GameStatePlaying
	GameStatePaused
	GameStateGameOver
)

// Evidence represents collectible evidence in the game
type Evidence struct {
	position     rl.Vector3
	evidenceType string
	collected    bool
	description  string
}

// NPC represents non-player characters
type NPC struct {
	position        rl.Vector3
	rotation        float32
	detectionRadius float32
	isAlerted       bool
	patrolPath      []rl.Vector3
	currentTarget   int
}

// Environment holds the 3D environment data
type Environment struct {
	buildings []Building
	props     []Prop
}

// Building represents 3D buildings in the scene
type Building struct {
	position rl.Vector3
	size     rl.Vector3
	color    rl.Color
}

// Prop represents interactive objects in the scene
type Prop struct {
	position    rl.Vector3
	size        rl.Vector3
	color       rl.Color
	interactive bool
	propType    string
}

// NewGame creates a new game instance
func NewGame() *Game {
	game := &Game{
		gameState: GameStateMenu,
		playerPos: rl.Vector3{X: 0, Y: 1, Z: 0},
	}

	// Initialize camera
	game.camera = rl.Camera3D{
		Position:   rl.Vector3{X: 10, Y: 10, Z: 10},
		Target:     rl.Vector3{X: 0, Y: 0, Z: 0},
		Up:         rl.Vector3{X: 0, Y: 1, Z: 0},
		Fovy:       45.0,
		Projection: rl.CameraPerspective,
	}

	// Initialize environment
	game.initializeEnvironment()
	game.initializeEvidence()
	game.initializeNPCs()

	return game
}

// initializeEnvironment sets up the 3D environment
func (g *Game) initializeEnvironment() {
	// Create some buildings representing Miami locations
	g.environment.buildings = []Building{
		{position: rl.Vector3{X: -20, Y: 0, Z: -20}, size: rl.Vector3{X: 8, Y: 6, Z: 8}, color: rl.DarkGray},
		{position: rl.Vector3{X: 20, Y: 0, Z: -20}, size: rl.Vector3{X: 6, Y: 8, Z: 6}, color: rl.Gray},
		{position: rl.Vector3{X: -20, Y: 0, Z: 20}, size: rl.Vector3{X: 10, Y: 4, Z: 10}, color: rl.LightGray},
		{position: rl.Vector3{X: 20, Y: 0, Z: 20}, size: rl.Vector3{X: 7, Y: 7, Z: 7}, color: rl.DarkGray},
	}

	// Add some props (crime scene elements)
	g.environment.props = []Prop{
		{position: rl.Vector3{X: 0, Y: 0.5, Z: 5}, size: rl.Vector3{X: 2, Y: 1, Z: 1}, color: rl.Brown, interactive: true, propType: "table"},
		{position: rl.Vector3{X: -5, Y: 0.5, Z: 0}, size: rl.Vector3{X: 1, Y: 1, Z: 1}, color: rl.Red, interactive: true, propType: "evidence_box"},
		{position: rl.Vector3{X: 5, Y: 0.5, Z: -5}, size: rl.Vector3{X: 1.5, Y: 1, Z: 1.5}, color: rl.DarkBrown, interactive: true, propType: "desk"},
	}
}

// initializeEvidence sets up evidence items
func (g *Game) initializeEvidence() {
	g.evidence = []Evidence{
		{position: rl.Vector3{X: 2, Y: 1, Z: 3}, evidenceType: "blood_sample", description: "Blood sample - Type AB negative"},
		{position: rl.Vector3{X: -3, Y: 1, Z: -2}, evidenceType: "knife", description: "Surgical knife - Dexter's signature tool"},
		{position: rl.Vector3{X: 7, Y: 1, Z: -3}, evidenceType: "photo", description: "Victim photograph"},
	}
}

// initializeNPCs sets up non-player characters
func (g *Game) initializeNPCs() {
	g.npcs = []NPC{
		{
			position:        rl.Vector3{X: -10, Y: 1, Z: -10},
			detectionRadius: 8.0,
			patrolPath: []rl.Vector3{
				{X: -10, Y: 1, Z: -10},
				{X: -15, Y: 1, Z: -10},
				{X: -15, Y: 1, Z: -15},
				{X: -10, Y: 1, Z: -15},
			},
		},
		{
			position:        rl.Vector3{X: 15, Y: 1, Z: 15},
			detectionRadius: 6.0,
			patrolPath: []rl.Vector3{
				{X: 15, Y: 1, Z: 15},
				{X: 12, Y: 1, Z: 15},
				{X: 12, Y: 1, Z: 12},
				{X: 15, Y: 1, Z: 12},
			},
		},
	}
}

// Update handles game logic updates
func (g *Game) Update() {
	switch g.gameState {
	case GameStateMenu:
		if rl.IsKeyPressed(rl.KeyEnter) {
			g.gameState = GameStatePlaying
		}
	case GameStatePlaying:
		g.updatePlayer()
		g.updateNPCs()
		g.checkEvidenceCollection()

		if rl.IsKeyPressed(rl.KeyEscape) {
			g.gameState = GameStatePaused
		}
	case GameStatePaused:
		if rl.IsKeyPressed(rl.KeyEscape) {
			g.gameState = GameStatePlaying
		}
	}
}

// updatePlayer handles player movement and camera
func (g *Game) updatePlayer() {
	// Player movement
	moveSpeed := float32(0.1)

	if rl.IsKeyDown(rl.KeyW) {
		g.playerPos.Z -= moveSpeed
	}
	if rl.IsKeyDown(rl.KeyS) {
		g.playerPos.Z += moveSpeed
	}
	if rl.IsKeyDown(rl.KeyA) {
		g.playerPos.X -= moveSpeed
	}
	if rl.IsKeyDown(rl.KeyD) {
		g.playerPos.X += moveSpeed
	}

	// Update camera to follow player
	g.camera.Position = rl.Vector3{
		X: g.playerPos.X + 5,
		Y: g.playerPos.Y + 8,
		Z: g.playerPos.Z + 5,
	}
	g.camera.Target = g.playerPos
}

// updateNPCs handles NPC behavior and patrol
func (g *Game) updateNPCs() {
	for i := range g.npcs {
		npc := &g.npcs[i]

		// Simple patrol behavior
		if len(npc.patrolPath) > 0 {
			target := npc.patrolPath[npc.currentTarget]
			direction := rl.Vector3Subtract(target, npc.position)
			distance := rl.Vector3Length(direction)

			if distance < 0.5 {
				npc.currentTarget = (npc.currentTarget + 1) % len(npc.patrolPath)
			} else {
				direction = rl.Vector3Normalize(direction)
				npc.position = rl.Vector3Add(npc.position, rl.Vector3Scale(direction, 0.02))
			}
		}

		// Check if player is detected
		playerDistance := rl.Vector3Distance(npc.position, g.playerPos)
		npc.isAlerted = playerDistance < npc.detectionRadius
	}
}

// checkEvidenceCollection handles evidence collection
func (g *Game) checkEvidenceCollection() {
	for i := range g.evidence {
		if !g.evidence[i].collected {
			distance := rl.Vector3Distance(g.evidence[i].position, g.playerPos)
			if distance < 2.0 && rl.IsKeyPressed(rl.KeyE) {
				g.evidence[i].collected = true
				fmt.Printf("Collected: %s\n", g.evidence[i].description)
			}
		}
	}
}

// Render handles all game rendering
func (g *Game) Render() {
	rl.BeginDrawing()
	rl.ClearBackground(rl.Black)

	switch g.gameState {
	case GameStateMenu:
		g.renderMenu()
	case GameStatePlaying:
		g.render3D()
		g.renderHUD()
	case GameStatePaused:
		g.render3D()
		g.renderPauseMenu()
	}

	rl.EndDrawing()
}

// renderMenu renders the main menu
func (g *Game) renderMenu() {
	rl.DrawText("DEXTER: DARK PASSENGER", screenWidth/2-200, screenHeight/2-100, 40, rl.Red)
	rl.DrawText("3D Investigation Game", screenWidth/2-120, screenHeight/2-50, 20, rl.White)
	rl.DrawText("Press ENTER to Start", screenWidth/2-100, screenHeight/2+20, 20, rl.Gray)
	rl.DrawText("WASD to Move, E to Collect Evidence", screenWidth/2-150, screenHeight/2+60, 16, rl.LightGray)
}

// render3D renders the 3D game world
func (g *Game) render3D() {
	rl.BeginMode3D(g.camera)

	// Draw ground
	rl.DrawPlane(rl.Vector3{X: 0, Y: 0, Z: 0}, rl.Vector2{X: 100, Y: 100}, rl.DarkGreen)

	// Draw buildings
	for _, building := range g.environment.buildings {
		rl.DrawCube(building.position, building.size.X, building.size.Y, building.size.Z, building.color)
		rl.DrawCubeWires(building.position, building.size.X, building.size.Y, building.size.Z, rl.Black)
	}

	// Draw props
	for _, prop := range g.environment.props {
		rl.DrawCube(prop.position, prop.size.X, prop.size.Y, prop.size.Z, prop.color)
		if prop.interactive {
			rl.DrawCubeWires(prop.position, prop.size.X+0.1, prop.size.Y+0.1, prop.size.Z+0.1, rl.Yellow)
		}
	}

	// Draw player
	rl.DrawCube(g.playerPos, 1, 2, 1, rl.Blue)
	rl.DrawCubeWires(g.playerPos, 1, 2, 1, rl.DarkBlue)

	// Draw NPCs
	for _, npc := range g.npcs {
		color := rl.Orange
		if npc.isAlerted {
			color = rl.Red
		}
		rl.DrawCube(npc.position, 1, 2, 1, color)

		// Draw detection radius
		rl.DrawCircle3D(npc.position, npc.detectionRadius, rl.Vector3{X: 1, Y: 0, Z: 0}, 90, rl.Fade(rl.Red, 0.1))
	}

	// Draw evidence
	for _, evidence := range g.evidence {
		if !evidence.collected {
			// Animate evidence with floating effect
			animY := evidence.position.Y + float32(math.Sin(float64(rl.GetTime()*3)))*0.2
			pos := rl.Vector3{X: evidence.position.X, Y: animY, Z: evidence.position.Z}
			rl.DrawCube(pos, 0.5, 0.5, 0.5, rl.Gold)
			rl.DrawCubeWires(pos, 0.5, 0.5, 0.5, rl.Yellow)
		}
	}

	rl.EndMode3D()
}

// renderHUD renders the game HUD
func (g *Game) renderHUD() {
	// Evidence counter
	collectedCount := 0
	for _, evidence := range g.evidence {
		if evidence.collected {
			collectedCount++
		}
	}

	rl.DrawText(fmt.Sprintf("Evidence: %d/%d", collectedCount, len(g.evidence)), 10, 10, 20, rl.White)

	// Detection status
	detected := false
	for _, npc := range g.npcs {
		if npc.isAlerted {
			detected = true
			break
		}
	}

	if detected {
		rl.DrawText("DETECTED!", screenWidth-120, 10, 20, rl.Red)
	} else {
		rl.DrawText("HIDDEN", screenWidth-80, 10, 20, rl.Green)
	}

	// Instructions
	rl.DrawText("WASD: Move | E: Collect | ESC: Pause", 10, screenHeight-30, 16, rl.LightGray)
}

// renderPauseMenu renders the pause menu
func (g *Game) renderPauseMenu() {
	rl.DrawRectangle(0, 0, screenWidth, screenHeight, rl.Fade(rl.Black, 0.5))
	rl.DrawText("PAUSED", screenWidth/2-60, screenHeight/2-20, 40, rl.White)
	rl.DrawText("Press ESC to Resume", screenWidth/2-80, screenHeight/2+20, 16, rl.Gray)
}

func main() {
	// Initialize raylib
	rl.InitWindow(screenWidth, screenHeight, gameTitle)
	rl.SetTargetFPS(60)

	// Create game instance
	game := NewGame()

	// Main game loop
	for !rl.WindowShouldClose() {
		game.Update()
		game.Render()
	}

	// Cleanup
	rl.CloseWindow()
}
