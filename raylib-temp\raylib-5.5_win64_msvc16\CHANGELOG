changelog
---------

Current Release:    raylib 5.5 (18 November 2024)

-------------------------------------------------------------------------
Release:     raylib 5.5 (18 November 2024)
-------------------------------------------------------------------------
KEY CHANGES:
 - New tool: raylib project creator
 - New rcore backends: RGFW and SDL3
 - New platforms supported: Dreamcast, N64, PSP, PSVita, PS4
 - Added GPU Skinning support (all platforms and GL versions)
 - Added raymath C++ operators

Detailed changes:

WIP: Last update with commit from 02-Nov-2024

[rcore] ADDED: Working directory info at initialization by @Ray
[rcore] ADDED: `GetClipboardImage()`, supported by multiple backends (#4459) by @evertonse
[rcore] ADDED: `MakeDirectory()`, supporting recursive directory creation by @Ray
[rcore] ADDED: `ComputeSHA1()` (#4390) by @<PERSON> Carbajal
[rcore] ADDED: `ComputeCRC32()` and `ComputeMD5()` by @Ray
[rcore] ADDED: `GetKeyName()` (#4161) by @MrScautHD
[rcore] ADDED: `IsFileNameValid()` by @Ray
[rcore] ADDED: `GetViewRay()`, viewport independent raycast (#3709) by @Luís Almeida
[rcore] RENAMED: `GetMouseRay()` to `GetScreenToWorldRay()` (#3830) by @Ray
[rcore] RENAMED: `GetViewRay()` to `GetScreenToWorldRayEx()` (#3830) by @Ray
[rcore] REVIEWED: `GetApplicationDirectory()` for FreeBSD (#4318) by @base
[rcore] REVIEWED: `LoadDirectoryFilesEx()`/`ScanDirectoryFiles()`, support directory on filter (#4302) by @foxblock
[rcore] REVIEWED: Update comments on fullscreen and boderless window to describe what they do (#4280) by @Jeffery Myers
[rcore] REVIEWED: Correct processing of mouse wheel on Automation events #4263 by @Ray
[rcore] REVIEWED: Fix gamepad axis movement and its automation event recording (#4184) by @maxmutant
[rcore] REVIEWED: Do not set RL_TEXTURE_FILTER_LINEAR when high dpi flag is enabled (#4189) by @Dave Green
[rcore] REVIEWED: `GetScreenWidth()`/`GetScreenHeight()` (#4074) by @Anthony Carbajal
[rcore] REVIEWED: Initial window dimensions checks (#3950) by @Christian Haas
[rcore] REVIEWED: Set default init values for random #3954 by @Ray
[rcore] REVIEWED: Window positioning, avoid out-of-screen window-bar by @Ray
[rcore] REVIEWED: Fix framerate recording for .gif (#3894) by @Rob Loach
[rcore] REVIEWED: Screen space related functions consistency (#3830) by @aiafrasinei
[rcore] REVIEWED: `GetFileNameWithoutExt()` (#3771) by @oblerion
[rcore] REVIEWED: `GetWindowScaleDPI()`, simplified (#3701) by @Karl Zylinski
[rcore] REVIEWED: `UnloadAutomationEventList()` (#3658) by @Antonis Geralis
[rcore] REVIEWED: Flip VR screens (#3633) by @Matthew Oros
[rcore] REVIEWED: Remove unused vScreenCenter (#3632) by @Matthew Oros
[rcore] REVIEWED: `LoadRandomSequence()`, issue in sequence generation #3612 by @Ray
[rcore] REVIEWED: `IsMouseButtonUp()` (#3609) by @Kenneth M
[rcore] REVIEWED: Fix typos in src/platforms/rcore_*.c (#3581) by @RadsammyT
[rcore] REVIEWED: `ExportDataAsCode()`, change sanitization check (#3837) by @Laurentino Luna
[rcore] REVIEWED: `ExportDataAsCode()`, add little sanitization to indentifier names (#3832) by @4rk
[rcore] REVIEWED: `GetScreenWidth()`/`GetScreenHeight()` align with all platforms (#4451) by @Arche Washi
[rcore] REVIEWED: `SetGamepadVibration()`, added duration parameter (#4410) by @Asdqwe -WARNING-
[rcore] REVIEWED: `GetGamepadAxisMovement()`, fix #4405 (#4420) by @Asdqwe
[rcore] REVIEWED: `GetGestureHoldDuration()` comments by @Ray
[rcore][rlgl] REVIEWED: Fix scale issues when ending a view mode (#3746) by @Jeffery Myers
[rcore][GLFW] REVIEWED: Keep CORE.Window.position properly in sync with glfw window position (#4190) by @Dave Green
[rcore][GLFW] REVIEWED: Set AUTO_ICONIFY flag to false per default (#4188) by @Dave Green
[rcore][GLFW] REVIEWED: `InitPlatform()`, add workaround for NetBSD (#4139) by @NishiOwO
[rcore][GLFW] REVIEWED: Fix window not initializing on primary monitor (#3923) by @Rafael Bordoni
[rcore][GLFW] REVIEWED: Set relative mouse mode when the cursor is disabled (#3874) by @Jeffery Myers
[rcore][GLFW] REVIEWED: Remove GLFW mouse passthrough hack and increase GLFW version in CMake (#3852) by @Alexandre Almeida
[rcore][GLFW] REVIEWED: Updated GLFW to 3.4 (#3827) by @Alexandre Almeida
[rcore][GLFW] REVIEWED: Feature test macros before include (#3737) by @John
[rcore][GLFW] REVIEWED: Fix inconsistent dll linkage warning on windows (#4447) by @Jeffery Myers
[rcore][Web] ADDED: `SetWindowOpacity()` implementation (#4403) by @Asdqwe
[rcore][Web] ADDED: `MaximizeWindow()` and `RestoreWindow()` implementations (#4397) by @Asdqwe
[rcore][Web] ADDED: `ToggleFullscreen()` implementation (#3634) by @ubkp
[rcore][Web] ADDED: `GetWindowPosition()` implementation (#3637) by @ubkp
[rcore][Web] ADDED: `ToggleBorderlessWindowed()` implementation (#3622) by @ubkp
[rcore][Web] ADDED: `GetMonitorWidth()` and `GetMonitorHeight()` implementations (#3636) by @ubkp
[rcore][Web] REVIEWED: Update `SetWindowState()` and `ClearWindowState()` to handle `FLAG_WINDOW_MAXIMIZED` (#4402) by @Asdqwe
[rcore][Web] REVIEWED: `WindowSizeCallback()`, do not try to handle DPI, already managed by GLFW (#4143) by @SuperUserNameMan
[rcore][Web] REVIEWED: Relative mouse mode issues (#3940) by @Cemal Gönültaş
[rcore][Web] REVIEWED: `ShowCursor()`, `HideCursor()` and `SetMouseCursor()` (#3647) by @ubkp
[rcore][Web] REVIEWED: Fix CORE.Input.Mouse.cursorHidden with callbacks (#3644) by @ubkp
[rcore][Web] REVIEWED: Fix `IsMouseButtonUp()` (#3611) by @ubkp
[rcore][Web] REVIEWED: HighDPI support #3372 by @Ray
[rcore][Web] REVIEWED: `SetWindowSize()` (#4452) by @Asdqwe
[rcore][Web] REVIEWED: `EmscriptenResizeCallback()`, simplified (#4415) by @Asdqwe
[rcore][SDL] ADDED: `IsCursorOnScreen()` (#3862) by @Peter0x44
[rcore][SDL] ADDED: Gamepad rumble/vibration support (#3819) by @GideonSerf
[rcore][SDL] REVIEWED: Gamepad support (#3776) by @A
[rcore][SDL] REVIEWED: `GetWorkingDirectory()`, return correct path (#4392) by @Asdqwe
[rcore][SDL] REVIEWED: `GetClipboardText()`, fix memory leak (#4354) by @Asdqwe
[rcore][SDL] REVIEWED: Change SDL_Joystick to SDL_GameController (#4129) by @Frank Kartheuser
[rcore][SDL] REVIEWED: Update storage base path, use provided SDL base path by @Ray
[rcore][SDL] REVIEWED: Call SDL_GL_SetSwapInterval() after GL context creation (#3997) by @JupiterRider
[rcore][SDL] REVIEWED: `GetKeyPressed()` (#3869) by @Arthur
[rcore][SDL] REVIEWED: Fix SDL multitouch tracking (#3810) by @mooff
[rcore][SDL] REVIEWED: Fix `SUPPORT_WINMM_HIGHRES_TIMER` (#3679) by @ubkp
[rcore][SDL] REVIEWED: SDL text input to Unicode codepoints #3650 by @Ray
[rcore][SDL] REVIEWED: `IsMouseButtonUp()` and add touch events (#3610) by @ubkp
[rcore][SDL] REVIEWED: Fix real touch gestures (#3614) by @ubkp
[rcore][SDL] REVIEWED: `IsKeyPressedRepeat()` (#3605) by @ubkp
[rcore][SDL] REVIEWED: `GetKeyPressed()` and `GetCharPressed()` for SDL (#3604) by @ubkp
[rcore][SDL] REVIEWED: `SetMousePosition()` for SDL (#3580) by @ubkp
[rcore][SDL] REVIEWED: `SetWindowIcon()` for SDL (#3578) by @ubkp
[rcore][SDL][rlgl] REVIEWED: Fix for running gles2 with SDL on desktop (#3542) by @_Tradam
[rcore][Android] REVIEWED: Issue with isGpuReady flag (#4340) by @Menno van der Graaf
[rcore][Android] REVIEWED: Allow main() to return it its caller on configuration changes (#4288) by @Hesham Abourgheba
[rcore][Android] REVIEWED: Replace deprecated Android function ALooper_pollAll with ALooper_pollOnce (#4275) by @Menno van der Graaf
[rcore][Android] REVIEWED: `PollInputEvents()`, register previous gamepad events (#3910) by @Aria
[rcore][Android] REVIEWED: Fix Android keycode translation and duplicate key constants (#3733) by @Alexandre Almeida
[rcore][DRM] ADDED: uConsole keys mapping (#4297) by @carverdamien
[rcore][DRM] ADDED: `GetMonitorWidth/Height()` (#3956) by @gabriel-marques
[rcore][DRM] REVIEWED: `IsMouseButtonUp()` (#3611) by @ubkp
[rcore][DRM] REVIEWED: Optimize gesture handling (#3616) by @ubkp
[rcore][DRM] REVIEWED: `IsKeyPressedRepeat()` for PLATFORM_DRM direct input (#3583) by @ubkp
[rcore][DRM] REVIEWED: Fix gamepad buttons not working in drm backend (#3888) by @MrMugame
[rcore][DRM] REVIEWED: DRM backend to only use one api to allow for more devices (#3879) by @MrMugame
[rcore][DRM] REVIEWED: Avoid separate thread when polling for gamepad events (#3641) by @Cinghy Creations
[rcore][DRM] REVIEWED: Connector status reported as UNKNOWN but should be considered as CONNECTED (#4305) by @Michał Jaskólski
[rcore][RGFW] ADDED: RGFW, new rcore backend platform (#3941) by @Colleague Riley
[rcore][RGFW] REVIEWED: RGFW 1.0 (#4144) by @Colleague Riley
[rcore][RGFW] REVIEWED: Fix errors when compiling with mingw (#4282) by @Colleague Riley
[rcore][RGFW] REVIEWED: Replace long switch with a lookup table (#4108) by @Colleague Riley
[rcore][RGFW] REVIEWED: Fix MSVC build errors (#4441) by @Colleague Riley
[rlgl] ADDED: More uniform data type options #4137 by @Ray
[rlgl] ADDED: Vertex normals for RLGL immediate drawing mode (#3866) by @bohonghuang -WARNING-
[rlgl] ADDED: `rlCullDistance*()` variables and getters (#3912) by @KotzaBoss
[rlgl] ADDED: `rlSetClipPlanes()` function (#3912) by @KotzaBoss
[rlgl] ADDED: `isGpuReady` flag, allow font loading with no GPU acceleration by @Ray -WARNING-
[rlgl] REVIEWED: Changed RLGL_VERSION from 4.5 to 5.0 (#3914) by @Mute
[rlgl] REVIEWED: Shader load failing returns 0, instead of fallback by @Ray -WARNING-
[rlgl] REVIEWED: Standalone mode default flags (#4334) by @Asdqwe
[rlgl] REVIEWED: Fix hardcoded index values in vboID array (#4312) by @Jett
[rlgl] REVIEWED: GLint64 did not exist before OpenGL 3.2 (#4284) by @Tchan0
[rlgl] REVIEWED: Extra warnings in case OpenGL 4.3 is not enabled (#4202) by @Maxim Knyazkin
[rlgl] REVIEWED: Using GLint64 for glGetBufferParameteri64v() (#4197) by @Randy Palamar
[rlgl] REVIEWED: Replace `glGetInteger64v()` with `glGetBufferParameteri64v()` (#4154) by @Kai Kitagawa-Jones
[rlgl] REVIEWED: `rlMultMatrixf()`, fix matrix multiplication order (#3935) by @bohonghuang
[rlgl] REVIEWED: `rlSetVertexAttribute()`, define last parameter as offset #3800 by @Ray
[rlgl] REVIEWED: `rlDisableVertexAttribute()`, remove redundat calls for SHADER_LOC_VERTEX_COLOR (#3871) by @Kacper Zybała
[rlgl] REVIEWED: `rlLoadTextureCubemap()`, load mipmaps for cubemaps (#4429) by @Nikolas
[rlgl] REVIEWED: `rlLoadFramebuffer()`, parameters not required by @Ray
[rlgl] REVIEWED: `rlSetUniformSampler()` (#3759) by @veins1
[rlgl] REVIEWED: Renamed near/far variables (#4039) by @jgabaut
[rlgl] REVIEWED: Expose OpenGL symbols (#3588) by @Peter0x44
[rlgl] REVIEWED: Fix OpenGL 1.1 build issues (#3876) by @Ray
[rlgl] REVIEWED: Fixed compilation for OpenGL ES (#4243) by @Maxim Knyazkin
[rlgl] REVIEWED: rlgl function description and comments by @Ray
[rlgl] REVIEWED: Expose glad functions when building raylib as a shared lib (#3572) by @Peter0x44
[rlgl] REVIEWED: Fix version info in rlgl.h (#3558) by @Steven Schveighoffer
[rlgl] REVIEWED: Use the vertex color to the base shader in GLSL330 (#4431) by @Jeffery Myers
[rcamera] REVIEWED: Make camera movement independant of framerate (#4247) by @hanaxars -WARNING-
[rcamera] REVIEWED: Updated camera speeds with GetFrameTime() (#4362) by @Anthony Carbajal
[rcamera] REVIEWED: `UpdateCamera()`, added CAMERA_CUSTOM check (#3938) by @Tomas Fabrizio Orsi
[rcamera] REVIEWED: Support mouse/keyboard and gamepad coexistence for input (#3579) by @ubkp
[rcamera] REVIEWED: Cleaned away unused macros(#3762) by @Brian E
[rcamera] REVIEWED: Fix for free camera mode (#3603) by @lesleyrs
[rcamera] REVIEWED: `GetCameraRight()` (#3784) by @Danil
[raymath] ADDED: C++ operator overloads for common math function (#4385) by @Jeffery Myers  -WARNING-
[raymath] ADDED: Vector4 math functions and Vector2 variants of some Vector3 functions (#3828) by @Bowserinator
[raymath] REVIEWED: Fix MSVC warnings/errors in C++ (#4125) by @Jeffery Myers
[raymath] REVIEWED: Add extern "C" to raymath header for C++ (#3978) by @Jeffery Myers
[raymath] REVIEWED: `QuaternionFromAxisAngle()`, remove redundant axis length calculation (#3900) by @jtainer
[raymath] REVIEWED: `Vector3Perpendicular()`, avoid implicit conversion from float to double (#3799) by @João Foscarini
[raymath] REVIEWED: `MatrixDecompose()`, incorrect output for certain scale and rotations (#4461) by @waveydave
[raymath] REVIEWED: Small code refactor (#3753) by @Idir Carlos Aliane
[rshapes] ADDED: `CheckCollisionCircleLine()` (#4018) by @kai-z99
[rshapes] REVIEWED: Multisegment Bezier splines (#3744) by @Santiago Pelufo
[rshapes] REVIEWED: Expose shapes drawing texture and rectangle (#3677) by @Jeffery Myers
[rshapes] REVIEWED: `DrawLine()` #4075 by @Ray
[rshapes] REVIEWED: `DrawPixel()` drawing by @Ray
[rshapes] REVIEWED: `DrawLine()` to avoid pixel rounding issues #3931 by @Ray
[rshapes] REVIEWED: `DrawRectangleLines()`, considering view matrix for lines "alignment" by @Ray
[rshapes] REVIEWED: `DrawRectangleLines()`, pixel offset (#4261) by @RadsammyT
[rshapes] REVIEWED: `DrawRectangleLines()`, pixel offset when scaling (#3884) by @Ray
[rshapes] REVIEWED: `DrawRectangleLinesEx()`, make sure accounts for square tiles (#4382) by @Jojaby
[rshapes] REVIEWED: `Draw*Gradient()` color parameter names (#4270) by @Paperdomo101
[rshapes] REVIEWED: `DrawGrid()`, remove duplicate color calls (#4148) by @Jeffery Myers
[rshapes] REVIEWED: `DrawSplineLinear()` to `SUPPORT_SPLINE_MITERS` by @Ray
[rshapes] REVIEWED: `DrawSplineLinear()`, implement miters (#3585) by @Toctave
[rshapes] REVIEWED: `CheckCollisionPointRec()` by @Ray
[rshapes] REVIEWED: `CheckCollisionPointCircle()`, new implementation (#4135) by @kai-z99
[rshapes] REVIEWED: `CheckCollisionCircles()`, optimized (#4065) by @kai-z99
[rshapes] REVIEWED: `CheckCollisionPointPoly()` (#3750) by @Antonio Raúl
[rshapes] REVIEWED: `CheckCollisionCircleRec()` (#3584) by @ubkp
[rshapes] REVIEWED: Add more detail to function comment (#4344) by @Jeffery Myers
[rshapes] REVIEWED: Functions that draw point arrays take them as const (#4051) by @Jeffery Myers
[rtextures] ADDED: `ColorIsEqual()` by @Ray
[rtextures] ADDED: `ColorLerp()`, to mix 2 colors together (#4310) by @SusgUY446
[rtextures] ADDED: `LoadImageAnimFromMemory()` (#3681) by @IoIxD
[rtextures] ADDED: `ImageKernelConvolution()` (#3528) by @Karim
[rtextures] ADDED: `ImageFromChannel()` (#4105) by @Bruno Cabral
[rtextures] ADDED: `ImageDrawLineEx()` (#4097) by @Le Juez Victor
[rtextures] ADDED: `ImageDrawTriangle()` (#4094) by @Le Juez Victor
[rtextures] REMOVED: SVG files loading and drawing, moving it to raylib-extras by @Ray -WARNING-
[rtextures] REVIEWED: `LoadImage()`, added support for 3-channel QOI images (#4384) by @R-YaTian
[rtextures] REVIEWED: `LoadImageRaw()` #3926 by @Ray
[rtextures] REVIEWED: `LoadImageColors()`, advance k in loop (#4120) by @Bruno Cabral
[rtextures] REVIEWED: `LoadTextureCubemap()`, added `mipmaps` #3665 by @Ray
[rtextures] REVIEWED: `LoadTextureCubemap()`, assign format to cubemap (#3823) by @Gary M
[rtextures] REVIEWED: `LoadTextureCubemap()`, load mipmaps for cubemaps (#4429) by @Nikolas
[rtextures] REVIEWED: `LoadTextureCubemap()`, avoid dangling re-allocated pointers (#4439) by @Nikolas
[rtextures] REVIEWED: `LoadImageFromScreen()`, fix scaling (#3881) by @proberge-dev
[rtextures] REVIEWED: `LoadImageFromMemory()`, warnings on invalid image data (#4179) by @Jutastre
[rtextures] REVIEWED: `LoadImageAnimFromMemory()`, added security checks (#3924) by @Ray
[rtextures] REVIEWED: `ImageColorTint()` and `ColorTint()`, optimized (#4015) by @Le Juez Victor
[rtextures] REVIEWED: `ImageKernelConvolution()`, formating and warnings by @Ray
[rtextures] REVIEWED: `ImageDrawRectangleRec`, fix bounds check (#3732) by @Blockguy24
[rtextures] REVIEWED: `ImageResizeCanvas()`, implemented fill color (#3720) by @Lieven Petersen
[rtextures] REVIEWED: `ImageDrawRectangleRec()` (#3721) by @Le Juez Victor
[rtextures] REVIEWED: `ImageDraw()`, don't try to blend images without alpha (#4395) by @Nikolas
[rtextures] REVIEWED: `GenImagePerlinNoise()` being stretched (#4276) by @Bugsia
[rtextures] REVIEWED: `GenImageGradientLinear()`, fix some angles (#4462) by @decromo
[rtextures] REVIEWED: `DrawTexturePro()` to avoid negative dest rec #4316 by @Ray
[rtextures] REVIEWED: `ColorToInt()`, fix undefined behaviour (#3996) by @OetkenPurveyorOfCode
[rtextures] REVIEWED: Remove panorama cubemap layout option (#4425) by @Jeffery Myers
[rtextures] REVIEWED: Removed unneeded module check, `rtextures` should not depend on `rtext` by @Ray
[rtextures] REVIEWED: Simplified for loop for some image manipulation functions (#3712) by @Alice Nyaa
[rtext] ADDED: BDF fonts support (#3735) by @Stanley Fuller -WARNING-
[rtext] ADDED: `TextToCamel()` (#4033) by @IoIxD
[rtext] ADDED: `TextToSnake()` (#4033) by @IoIxD
[rtext] ADDED: `TextToFloat()` (#3627) by @Benjamin Schmid Ties
[rtext] REDESIGNED: `SetTextLineSpacing()` by @Ray -WARNING-
[rtext] REVIEWED: `LoadFontDataBDF()` name and formating by @Ray
[rtext] REVIEWED: `LoadFontDefault()`, initialize glyphs and recs to zero #4319 by @Ray
[rtext] REVIEWED: `LoadFontEx()`, avoid default font fallback (#4077) by @Peter0x44 -WARNING-
[rtext] REVIEWED: `LoadBMFont()`, extended functionality (#3536) by @Dongkun Lee
[rtext] REVIEWED: `LoadBMFont()`, issue on not glyph data initialized by @Ray
[rtext] REVIEWED: `LoadFontFromMemory()`, use strncpy() to fix buffer overflow (#3795) by @Mingjie Shen
[rtext] REVIEWED: `LoadCodepoints()` returning a freed ptr when count is 0 (#4089) by @Alice Nyaa
[rtext] REVIEWED: `LoadFontData()` avoid fallback glyphs by @Ray -WARNING-
[rtext] REVIEWED: `LoadFontData()`, load image only if glyph has been found in font by @Ray
[rtext] REVIEWED: `ExportFontAsCode()`, fix C++ compiler errors (#4013) by @DarkAssassin23
[rtext] REVIEWED: `MeasureTextEx()` height calculation (#3770) by @Marrony Neris
[rtext] REVIEWED: `MeasureTextEx()`, additional check for empty input string (#4448) by @mpv-enjoyer
[rtext] REVIEWED: `CodepointToUTF8()`, clean static buffer #4379 by @Ray
[rtext] REVIEWED: `TextToFloat()`, always multiply by sign (#4273) by @listeria
[rtext] REVIEWED: `TextReplace()` const correctness (#3678) by @maverikou
[rtext] REVIEWED: `TextToFloat()`, coding style (#3627) by @Benjamin Schmid Ties
[rtext] REVIEWED: Some comments to align to style (#3756) by @Idir Carlos Aliane
[rtext] REVIEWED: Adjust font atlas area calculation so padding area is not underestimated at small font sizes (#3719) by @Tim Romero
[rmodels] ADDED: GPU skinning support for models animations (#4321) by @Daniel Holden -WARNING-
[rmodels] ADDED: Support 16-bit unsigned short vec4 format for gltf joint loading (#3821) by @Gary M
[rmodels] ADDED: Support animation names for the m3d model format (#3714) by @kolunmi
[rmodels] ADDED: `DrawModelPoints()`, more performant point cloud rendering (#4203) by @Reese Gallagher
[rmodels] ADDED: `ExportMeshAsCode()` by @Ray
[rmodels] REVIEWED: Multiple updates to gltf loading, improved macro (#4373) by @Harald Scheirich
[rmodels] REVIEWED: `LoadOBJ()`, correctly split obj meshes by material (#4285) by @Jeffery Myers
[rmodels] REVIEWED: `LoadOBJ()`, add warning when loading an OBJ with multiple materials (#4271) by @Jeffery Myers
[rmodels] REVIEWED: `LoadOBJ()`, fix bug that fragmented the loaded meshes (#4494) by @Eike Decker
[rmodels] REVIEWED: `LoadIQM()`, set model.meshMaterial[] (#4092) by @SuperUserNameMan
[rmodels] REVIEWED: `LoadIQM()`, attempt to load texture from IQM at loadtime (#4029) by @Jett
[rmodels] REVIEWED: `LoadM3D(), fix vertex colors for m3d files (#3859) by @Jeffery Myers
[rmodels] REVIEWED: `LoadGLTF()`, supporting additional vertex data formats (#3546) by @MrScautHD
[rmodels] REVIEWED: `LoadGLTF()`, correctly handle the node hierarchy in a glTF file (#4037) by @Paul Melis
[rmodels] REVIEWED: `LoadGLTF()`, replaced SQUAD quat interpolation with cubic hermite (gltf 2.0 specs) (#3920) by @Benji
[rmodels] REVIEWED: `LoadGLTF()`, support 2nd texture coordinates loading by @Ray
[rmodels] REVIEWED: `LoadGLTF()`, support additional vertex attributes data formats #3890 by @Ray
[rmodels] REVIEWED: `LoadGLTF()`, set cgltf callbacks to use `LoadFileData()` and `UnloadFileData()` (#3652) by @kolunmi
[rmodels] REVIEWED: `LoadGLTF()`, JOINTS loading #3836 by @Ray
[rmodels] REVIEWED: `LoadImageFromCgltfImage()`, fix base64 padding support (#4112) by @SuperUserNameMan
[rmodels] REVIEWED: `LoadModelAnimationsIQM()`, fix corrupted animation names (#4026) by @Jett
[rmodels] REVIEWED: `LoadModelAnimationsGLTF()`, load animations with 1 frame (#3804) by @Nikita Blizniuk
[rmodels] REVIEWED: `LoadModelAnimationsGLTF()`, added missing interpolation types (#3919) by @Benji
[rmodels] REVIEWED: `LoadModelAnimationsGLTF()` (#4107) by @VitoTringolo
[rmodels] REVIEWED: `LoadBoneInfoGLTF()`, add check for animation name being NULL (#4053) by @VitoTringolo
[rmodels] REVIEWED: `GenMeshSphere()`, fix artifacts (#4460) by @MikiZX1
[rmodels] REVIEWED: `GenMeshTangents()`, read uninitialized values, fix bounding case (#4066) by @kai-z99
[rmodels] REVIEWED: `GenMeshTangents()`, fixed out of bounds error (#3990) by @Salvador Galindo
[rmodels] REVIEWED: `UpdateModelAnimation()`, performance speedup (#4470) by @JettMonstersGoBoom
[rmodels] REVIEWED: `DrawCylinder()`, fix drawing due to imprecise angle (#4034) by @Paul Melis
[rmodels] REVIEWED: `DrawCylinder()`, fix drawing of cap (#4478) by @JeffM2501
[rmodels] REVIEWED: `DrawMesh()`, send full matModel to shader in DrawMesh (#4005) (#4022) by @David Holland
[rmodels] REVIEWED: `DrawMesh()`, fix material specular map retrieval (#3758) by @Victor Gallet
[rmodels] REVIEWED: `DrawModelEx()`, simplified multiplication of colors (#4002) by @Le Juez Victor
[rmodels] REVIEWED: `DrawBillboardPro()`, to be consistend with `DrawTexturePro()` (#4132) by @bohonghuang
[rmodels] REVIEWED: `DrawSphereEx()` optimization (#4106) by @smalltimewizard
[raudio] REVIEWED: `LoadMusicStreamFromMemory()`, support 24-bit FLACs (#4279) by @konstruktor227
[raudio] REVIEWED: `LoadWaveSamples()`, fix mapping of wave data (#4062) by @listeria
[raudio] REVIEWED: `LoadMusicStream()`, remove drwav_uninit() (#3986) by @FishingHacks
[raudio] REVIEWED: `LoadMusicStream()` qoa and wav loading (#3966) by @veins1
[raudio] REVIEWED: `ExportWaveAsCode()`, segfault (#3769) by @IoIxD
[raudio] REVIEWED: `WaveCrop()`, fix issues and use frames instead of samples (#3994) by @listeria
[raudio] REVIEWED: Crash from multithreading issues (#3907) by @Christian Haas
[raudio] REVIEWED: Reset music.ctxType if loading wasn't succesful (#3917) by @veins1
[raudio] REVIEWED: Added missing functions in "standalone" mode (#3760) by @Alessandro Nikolaev
[raudio] REVIEWED: Disable unused miniaudio features (#3544) by @Alexandre Almeida
[raudio] REVIEWED: Fix crash when switching playback device at runtime (#4102) by @jkaup
[raudio] REVIEWED: Support 24 bits samples for FLAC format (#4058) by @Alexey Kutepov
[examples] ADDED: `core_random_sequence` (#3846) by @Dalton Overmyer
[examples] ADDED: `core_input_virtual_controls` (#4342) by @oblerion
[examples] ADDED: `shapes_rectangle_advanced `, implementing `DrawRectangleRoundedGradientH()` (#4435) by @Everton Jr.
[examples] ADDED: `models_bone_socket` (#3833) by @iP
[examples] ADDED: `shaders_vertex_displacement` (#4186) by @Alex ZH
[examples] ADDED: `shaders_shadowmap` (#3653) by @TheManTheMythTheGameDev
[examples] REVIEWED: `core_2d_camera_platformer` by @Ray
[examples] REVIEWED: `core_2d_camera_mouse_zoom`, use logarithmic scaling for a 2d zoom functionality (#3977) by @Mike Will
[examples] REVIEWED: `core_input_gamepad_info`, all buttons displayed within the window (#4241) by @Asdqwe
[examples] REVIEWED: `core_input_gamepad_info`, show ps3 controller (#4040) by @Konrad Gutvik Grande
[examples] REVIEWED: `core_input_gamepad`, add drawing for generic gamepad (#4424) by @Asdqwe
[examples] REVIEWED: `core_input_gamepad`, add deadzone handling (#4422) by @Asdqwe
[examples] REVIEWED: `shapes_bouncing_ball` (#4226) by @Anthony Carbajal
[examples] REVIEWED: `shapes_following_eyes` (#3710) by @Hongyu Ouyang
[examples] REVIEWED: `shapes_draw_rectangle_rounded` by @Ray
[examples] REVIEWED: `shapes_draw_ring`, fix other examples (#4211) by @kai-z99
[examples] REVIEWED: `shapes_lines_bezier` by @Ray
[examples] REVIEWED: `textures_image_kernel` #3556 by @Ray
[examples] REVIEWED: `text_input_box` (#4229) by @Anthony Carbajal
[examples] REVIEWED: `text_writing_anim` (#4230) by @Anthony Carbajal
[examples] REVIEWED: `models_billboard` by @Ray
[examples] REVIEWED: `models_cubicmap` by @Ray
[examples] REVIEWED: `models_point_rendering` by @Ray
[examples] REVIEWED: `models_box_collisions` (#4224) by @Anthony Carbajal
[examples] REVIEWED: `models_skybox`, do not use HDR by default (#4115) by @Jeffery Myers
[examples] REVIEWED: `shaders_basic_pbr` (#4225) by @Anthony Carbajal
[examples] REVIEWED: `shaders_palette_switch` by @Ray
[examples] REVIEWED: `shaders_hybrid_render` (#3908) by @Yousif
[examples] REVIEWED: `shaders_lighting_instancing`, fix vertex shader (#4056) by @Karl Zylinski
[examples] REVIEWED: `shaders_raymarching`, add `raymarching.fs` for GLSL120 (#4183) by @CDM15y
[examples] REVIEWED: `shaders_shadowmap`, fix shaders for GLSL 1.20 (#4167) by @CDM15y
[examples] REVIEWED: `shaders_deferred_render` (#3655) by @Jett
[examples] REVIEWED: `shaders_basic_pbr` (#3621) by @devdad
[examples] REVIEWED: `shaders_basic_pbr`, remove dependencies (#3649) by @TheManTheMythTheGameDev
[examples] REVIEWED: `shaders_basic_pbr`, added more comments by @Ray
[examples] REVIEWED: `shaders_gpu_skinning`, to work with OpenGL ES 2.0 #4412 by @Ray
[examples] REVIEWED: `shaders_model_shader`, use free camera (#4428) by @IcyLeave6109
[examples] REVIEWED: `audio_stream_effects` (#3618) by @lipx
[examples] REVIEWED: `audio_raw_stream` (#3624) by @riadbettole
[examples] REVIEWED: `audio_mixed_processor` (#4214) by @Anthony Carbajal
[examples] REVIEWED: `raylib_opengl_interop`, fix building on PLATFORM_DESKTOP_SDL (#3826) by @Peter0x44
[examples] REVIEWED: Update examples missing UnloadTexture() calls (#4234) by @Anthony Carbajal
[examples] REVIEWED: Added GLSL 100 and 120 shaders to lightmap example (#3543) by @Jussi Viitala
[examples] REVIEWED: Set FPS to always 60 in all exampels (#4235) by @Anthony Carbajal
[build] REVIEWED: Makefile by @Ray
[build] REVIEWED: Makefile, fix wrong flag #3593 by @Ray
[build] REVIEWED: Makefile, disable wayland by default (#4369) by @Anthony Carbajal
[build] REVIEWED: Makefile, VSCode, fix to support multiple .c files (#4391) by @Alan Arrecis
[build] REVIEWED: Makefile, fix -Wstringop-truncation warning (#4096) by @Peter0x44
[build] REVIEWED: Makefile, fix issues for RGFW on Linux/macOS (#3969) by @Colleague Riley
[build] REVIEWED: Makefile, update RAYLIB_VERSION (#3901) by @Belllg
[build] REVIEWED: Makefile, use mingw32-make for Windows (#4436) by @Asdqwe
[build] REVIEWED: Makefile, move CUSTOM_CFLAGS for better visibility (#4054) by @Lázaro Albuquerque
[build] REVIEWED: Makefile, update emsdk paths to latest versions by @Ray
[build] REVIEWED: Makefile examples, align /usr/local with /src Makefile (#4286) by @Tchan0
[build] REVIEWED: Makefile examples, added `textures_image_kernel` (#3555) by @Sergey Zapunidi
[build] REVIEWED: Makefile examples (#4209) by @Anthony Carbajal
[build] REVIEWED: Makefile examples, to work on NetBSD (#4438) by @NishiOwO
[build] REVIEWED: Makefile examples, WebGL2 (OpenGL ES 3.0) backend flags #4330 by @Ray
[build] REVIEWED: Makefile examples, web building (#4434) by @Asdqwe
[build] REVIEWED: build.zig, fix various issues around `-Dconfig` (#4398) by @Sage Hane
[build] REVIEWED: build.zig, fix type mismatch (#4383) by @yuval_dev
[build] REVIEWED: build.zig, minor fixes (#4381) by @Sage Hane
[build] REVIEWED: build.zig, fix @src logic and a few things (#4380) by @Sage Hane
[build] REVIEWED: build.zig, improve logic (#4375) by @Sage Hane
[build] REVIEWED: build.zig, issues  (#4374) by @William Culver
[build] REVIEWED: build.zig, issues (#4366) by @Visen
[build] REVIEWED: build.zig, support desktop backend change (#4358) by @Nikolas
[build] REVIEWED: build.zig, use zig fmt (#4242) by @freakmangd
[build] REVIEWED: build.zig, check if wayland-scanner is installed (#4217) by @lnc3l0t
[build] REVIEWED: build.zig, override config.h definitions (#4193) by @lnc3l0t
[build] REVIEWED: build.zig, support GLFW platform detection (#4150) by @InventorXtreme
[build] REVIEWED: build.zig, make emscripten build compatible with Zig 0.13.0 (#4121) by @Mike Will
[build] REVIEWED: build.zig, pass the real build.zig file (#4113) by @InKryption
[build] REVIEWED: build.zig, leverage `dependencyFromBuildZig` (#4109) by @InKryption
[build] REVIEWED: build.zig, run examples from their directories (#4063) by @Mike Will
[build] REVIEWED: build.zig, fix raygui build when using addRaygui externally (#4027) by @Viktor Pocedulić
[build] REVIEWED: build.zig, fix emscripten build (#4012) by @Dylan
[build] REVIEWED: build.zig, update to zig 0.12.0dev while keeping 0.11.0 compatibility (#3715) by @freakmangd
[build] REVIEWED: build.zig, drop support for 0.11.0 and use more idiomatic build script code (#3927) by @freakmangd
[build] REVIEWED: build.zig, sdd shared library build option and update to zig 0.12.0-dev.2139 (#3727) by @Andrew Lee
[build] REVIEWED: build.zig, add `opengl_version` option (#3979) by @Alexei Mozaidze
[build] REVIEWED: build.zig, fix local dependency break (#3913) by @freakmangd
[build] REVIEWED: build.zig, fix breaking builds for Zig v0.11.0 (#3896) by @iarkn
[build] REVIEWED: build.zig, update to latest version and simplify (#3905) by @freakmangd
[build] REVIEWED: build.zig, remove all uses of deps/mingw (#3805) by @Peter0x44
[build] REVIEWED: build.zig, fixed illegal instruction crash (#3682) by @WisonYe
[build] REVIEWED: build.zig, fix broken build on #3863 (#3891) by @Nikolas Mauropoulos
[build] REVIEWED: build.zig, improve cross-compilation (#4468) by @deathbeam
[build] REVIEWED: CMake, update to raylib 5.0 (#3623) by @Peter0x44
[build] REVIEWED: CMake, added PLATFORM option for Desktop SDL (#3809) by @mooff
[build] REVIEWED: CMake, fix GRAPHICS_* check (#4359) by @Kacper Zybała
[build] REVIEWED: CMake, examples projects  (#4332) by @Ridge3Dproductions
[build] REVIEWED: CMake, fix warnings in projects/CMake/CMakeLists.txt (#4278) by @Peter0x44
[build] REVIEWED: CMake, delete BuildOptions.cmake (#4277) by @Peter0x44
[build] REVIEWED: CMake, update version to 5.0 so libraries are correctly versioned (#3615) by @David Williams
[build] REVIEWED: CMake, improved linkage flags to save 28KB on the final bundle (#4177) by @Lázaro Albuquerque
[build] REVIEWED: CMake, support OpenGL ES3 in `LibraryConfigurations.cmake` (#4079) by @manuel5975p
[build] REVIEWED: CMake, `config.h` fully available to users (#4044) by @Lázaro Albuquerque
[build] REVIEWED: CMake, pass -sFULL_ES3 instead of -sFULL_ES3=1 (#4090) by @manuel5975p
[build] REVIEWED: CMake, SDL build link the glfw dependency (#3860) by @Rob Loach
[build] REVIEWED: CMake, infer CMAKE_MODULE_PATH in super-build (#4042) by @fruzitent
[build] REVIEWED: CMake, remove USE_WAYLAND option (#3851) by @Alexandre Almeida
[build] REVIEWED: CMake, disable SDL rlgl_standalone example (#3861) by @Rob Loach
[build] REVIEWED: CMake, bump version required to avoid deprecated #3639 by @Ray
[build] REVIEWED: CMake, fix examples linking -DPLATFORM=SDL (#3825) by @Peter0x44
[build] REVIEWED: CMake, don't build for wayland by default (#4432) by @Peter0x44
[build] REVIEWED: VS2022, misc improvements by @Ray
[build] REVIEWED: VS2022, fix build warnings (#4095) by @Jeffery Myers
[build] REVIEWED: VS2022, added new examples (#4492) by @Jeffery Myers
[build] REVIEWED: Fix fix-build-paths (#3849) by @Caleb Barger
[build] REVIEWED: Fix build paths (#3835) by @Steve Biedermann
[build] REVIEWED: Fix VSCode sample project for macOS (#3666) by @Tim Romero
[build] REVIEWED: Fix some warnings on web builds and remove some redundant flags (#4069) by @Lázaro Albuquerque
[build] REVIEWED: Fix examples not building with gestures system disabled (#4020) by @Sprix
[build] REVIEWED: Fix GLFW runtime platform detection (#3863) by @Alexandre Almeida
[build] REVIEWED: Fix DRM cross-compile without sysroot (#3839) by @Christian W. Zuckschwerdt
[build] REVIEWED: Fix cmake-built libraylib.a to properly include GLFW's object files (#3598) by @Peter0x44
[build] REVIEWED: Hide unneeded internal symbols when building raylib as an so or dylib (#3573) by @Peter0x44
[build] REVIEWED: Corrected the path of android ndk toolchains for OSX platforms (#3574) by @Emmanuel Méra
[build][CI] ADDED: Automatic update for raylib_api.* (#3692) by @seiren
[build][CI] REVIEWED: Update workflows to use latest actions/upload-artifact by @Ray
[build][CI] REVIEWED: CodeQL minor tweaks to avoid some warnings by @Ray
[build][CI] REVIEWED: Update linux_examples.yml by @Ray
[build][CI] REVIEWED: Update linux.yml by @Ray
[build][CI] REVIEWED: Update webassembly.yml by @Ray
[build][CI] REVIEWED: Update cmake.yml by @Ray
[build][CI] REVIEWED: Update codeql.yml, exclude src/external files by @Ray
[bindings] ADDED: raylib-APL (#4253) by @Brian E
[bindings] ADDED: raylib-bqn, moved rayed-bqn (#4331) by @Brian E
[bindings] ADDED: brainfuck binding (#4169) by @_Tradam
[bindings] ADDED: raylib-zig-bindings (#4004) by @Lionel Briand
[bindings] ADDED: Raylib-CSharp wrapper (#3963) by @MrScautHD
[bindings] ADDED: COBOL binding (#3661) by @glowiak
[bindings] ADDED: raylib-beef binding (#3640) by @Braedon Lewis
[bindings] ADDED: Raylib-CSharp-Vinculum (#3571) by @Danil
[bindings] REVIEWED: Remove broken-link bindings #3899 by @Ray
[bindings] REVIEWED: Updated some versions on BINDINGS.md by @Ray
[bindings] REVIEWED: Removed umaintained repos (#3999) by @Antonis Geralis
[bindings] REDESIGNED: Add binding link to name, instead of separate column (#3995) by @Carmine Pietroluongo
[bindings] UPDATED: h-raylib (#4378) by @Anand Swaroop
[bindings] UPDATED: Raylib.lean, to master version (#4337) by @Daniil Kisel
[bindings] UPDATED: raybit, to latest master (#4311) by @Alex
[bindings] UPDATED: dray binding (#4163) by @red thing
[bindings] UPDATED: Julia (#4068) by @ShalokShalom
[bindings] UPDATED: nim to latest master (#3999) by @Antonis Geralis
[bindings] UPDATED: raylib-rs (#3991) by @IoIxD
[bindings] UPDATED: raylib-zig version (#3902) by @Nikolas
[bindings] UPDATED: raylib-odin (#3868) by @joyousblunder
[bindings] UPDATED: Raylib VAPI (#3829) by @Alex Macafee
[bindings] UPDATED: Raylib-cs (#3774) by @Brandon Baker
[bindings] UPDATED: h-raylib (#3739) by @Anand Swaroop
[bindings] UPDATED: OCaml bindings version (#3730) by @Tobias Mock
[bindings] UPDATED: Raylib.c3 (#3689) by @Kenta
[bindings] UPDATED: ray-cyber to 5.0 (#3654) by @fubark
[bindings] UPDATED: raylib-freebasic binding (#3591) by @WIITD
[bindings] UPDATED: SmallBASIC (#3562) by @Chris Warren-Smith
[bindings] UPDATED: Python raylib-py v5.0.0beta1 (#3557) by @Jorge A. Gomes
[bindings] UPDATED: raylib-d binding (#3561) by @Steven Schveighoffer
[bindings] UPDATED: Janet (#3553) by @Dmitry Matveyev
[bindings] UPDATED: Raylib.nelua (#3552) by @Auz
[bindings] UPDATED: raylib-cpp to 5.0 (#3551) by @Rob Loach
[bindings] UPDATED: Pascal binding (#3548) by @Gunko Vadim
[external] UPDATED: stb_truetype.h to latest version by @Ray
[external] UPDATED: stb_image_resize2.h to latest version by @Ray
[external] UPDATED: stb_image.h to latest version by @Ray
[external] UPDATED: qoa.h to latest version by @Ray
[external] UPDATED: dr_wav.h to latest version by @Ray
[external] UPDATED: dr_mp3.h to latest version by @Ray
[external] UPDATED: cgltf.h to latest version by @Ray
[external] REVIEWED: rl_gputex, correctly load mipmaps from DDS files (#4399) by @Nikolas
[external] REVIEWED: stb_image_resize2, dix vld1q_f16 undeclared in arm (#4309) by @masnm
[external] REVIEWED: miniaudio, fix library and Makefile for NetBSD (#4212) by @NishiOwO
[external] REVIEWED: raygui, update to latest version 4.5-dev (#4238) by @Anthony Carbajal
[external] REVIEWED: jar_xml, replace unicode characters by ascii characters to avoid warning in MSVC (#4196) by @Rico P
[external] REVIEWED: vox_loader, normals and new voxels shader (#3843) by @johann nadalutti
[parser] REVIEWED: README.md, to mirror fixed help text (#4336) by @Daniil Kisel
[parser] REVIEWED: Fix seg fault with long comment lines (#4306) by @Chris Warren-Smith
[parser] REVIEWED: Don't crash for files that don't end in newlines (#3981) by @Peter0x44
[parser] REVIEWED: Issues in usage example help text (#4084) by @Peter0x44
[parser] REVIEWED: Fix parsing of empty parentheses (#3974) by @Filyus
[parser] REVIEWED: Address parsing issue when generating XML #3893 by @Ray
[parser] REVIEWED: `MemoryCopy()`, prevent buffer overflow by replacing hard-coded arguments (#4011) by @avx0
[misc] ADDED: Create logo/raylib.icns by @Ray
[misc] ADDED: Create logo/raylib_1024x1024.png by @Ray
[misc] ADDED: Default vertex/fragment shader for OpenGL ES 3.0 (#4178) by @Lázaro Albuquerque
[misc] REVIEWED: README.md, fix Reddit badge (#4136) by @Ninad Sachania
[misc] REVIEWED: .gitignore, ignore compiled dll binaries (#3628) by @2Bear
[misc] REVIEWED: Fix undesired scrollbars on web shell files (#4104) by @jspast
[misc] REVIEWED: Made comments on raylib.h match comments in rcamera.h (#3942) by @Tomas Fabrizio Orsi
[misc] REVIEWED: Make raylib/raygui work better on touchscreen (#3728) by @Hongyu Ouyang
[misc] REVIEWED: Update config.h by @Ray

-------------------------------------------------------------------------
Release:     raylib 5.0 - 10th Anniversary Edition (18 November 2023)
-------------------------------------------------------------------------
KEY CHANGES:
 - REDESIGNED: rcore module platform-split, by @ubkp, @michaelfiber, @Bigfoot71, @raysan5
 - ADDED: New platform backend supported: SDL
 - ADDED: New platform backend supported: Nintendo Switch (closed source)
 - ADDED: New Splines drawing and evaluation API
 - ADDED: New pseudo-random numbers generator: rprand
 - ADDED: Automation Events System API
 - UPDATED: raygui 4.0: New version of this immediate-mode gui system for tools development with raylib

Detailed changes:
[rcore] ADDED: RAYLIB_VERSION_* values to raylib.h (#2856) by @RobLoach
[rcore] ADDED: IsKeyPressedRepeat() on PLATFORM_DESKTOP (#3245) by @actondev
[rcore] ADDED: SetWindowTitle() for PLATFORM_WEB (#3222) by @VitusVeit
[rcore] ADDED: FLAG_WINDOW_RESIZABLE for web (#3305) by @Peter0x44
[rcore] ADDED: SetWindowMaxSize() for desktop and web (#3309) by @ubkp
[rcore] ADDED: SetMouseCursor() for PLATFORM_WEB (#3414) by @BeardedBread
[rcore] ADDED: LoadRandomSequence()/UnloadRandomSequence() by @raysan5
[rcore] REMOVED: PLATFORM_RPI (#3232) by @michaelfiber
[rcore] REVIEWED: GetFileLength(), added comment (#3262) by @raysan5
[rcore] REVIEWED: Default shaders precission issue on PLATFORM_WEB (#3261) by @branc116
[rcore] REVIEWED: IsKey*() key validation checks (#3256) by @n77y
[rcore] REVIEWED: SetClipboardText() for PLATFORM_WEB (#3257) by @ubkp
[rcore] REVIEWED: Check if Ctrl modifier is among the currently set modifiers (#3230) by @mohad12211
[rcore] REVIEWED: Android app black screen when reopening by @Bigfoot71
[rcore] REVIEWED: Warnings when casting int to floats (#3218) by @JeffM2501
[rcore] REVIEWED: GetCurrentMonitor() detection inconsistency issue (#3215) by @ubkp
[rcore] REVIEWED: SetWindowMonitor() to no longer force fullscreen (#3209) by @ubkp
[rcore] REVIEWED: Fix mouse wheel not working in PLATFORM_RPI or PLATFORM_DRM (#3193) by @ubkp
[rcore] REVIEWED: GetMonitorName() description (#3184) (#3189) by @danilwhale
[rcore] REVIEWED: BeginScissorMode(), identify rendering to texture (#3510) by @gulrak
[rcore] REVIEWED: Window flags order (#3114) by @lesleyrs
[rcore] REVIEWED: Full movement for right analog stick (#3095) by @PixelPhobicGames
[rcore] REVIEWED: Fix Android app freeze after calling CloseWindow() (#3067) by @Bigfoot71
[rcore] REVIEWED: Lazy loading of default font used on image drawing (no InitWindow) by @raysan5
[rcore] REVIEWED: Minor tweaks to raylib events automation system @raysan5
[rcore] REVIEWED: GetCurrentMonitor() bugfix (#3058) by @hamyyy
[rcore] REVIEWED: Update CORE.Input.Touch.pointCount (#3024) by @raysan5
[rcore] REVIEWED: Mouse offset and scaling must be considered also on web!
[rcore] REVIEWED: CompressData(), possible stack overflow
[rcore] REVIEWED: GetWorldToScreenEx() (#3351) by @Brian-ED
[rcore] REVIEWED: Fix GetMouseDelta() issue for Android (#3404) by @Bigfoot71
[rcore] REVIEWED: GetFPS(), reset FPS averages when window is inited (#3445) by @JeffM2501
[rcore] REVIEWED: GetCurrentMonitor(), check window center position by @M374LX
[rcore] REVIEWED: GetRender*() issue on macOS highDPI (#3367) by @raysan5
[rcore] REVIEWED: ScanDirectoryFiles*(), paths building slashes sides (#3507)
[rlgl] ADDED: Experimental support for OpenGL ES 3.0 by @raysan5
[rlgl] ADDED: Support 16-Bit HDR textures (#3220) by @Not-Nik
[rlgl] ADDED: rlEnablePointMode() (#3490) by @JettMonstersGoBoom
[rlgl] ADDED: rlBlitFramebuffer(), required for deferred render
[rlgl] REVIEWED: LoadModel(), removed cube fallback mechanism (#3459)
[rlgl] REVIEWED: Improved support for ES3/WebGL2 (#3107) by @chemaguerra
[rlgl] REVIEWED: OpenGL 2.1 half floats support as part of an extension by @Not-Nik
[rlgl] REVIEWED: Avoid shader attribute not found log by @raysan5
[rlgl] REVIEWED: Avoid tracelog about not found uniforms (#3003) by @raysan5
[rlgl] REVIEWED: rLoadTexture() UBSAN complaints #1891 (#3321) by @Codom
[rlgl] REVIEWED: glInternalFormat as unsigned int
[rlgl] REVIEWED: OpenGL ES 3.0 support
[rshapes] ADDED: Spline drawing functions by @raysan5
[rshapes] ADDED: GetSplinePoint*() functions for spline evaluation by @raysan5
[rshapes] ADDED: DrawCircleLinesV() for consistency (#3452) by @Peter0x44
[rshapes] REVIEWED: DrawSplineCatmullRom() by @raysan5
[rshapes] REVIEWED: Minor fix in DrawLineBezier* (#3006) by @eternalStudent
[rshapes] REVIEWED: GetCollisionRec(), more performant (#3052) by @manuel5975p
[rshapes] REVIEWED: Fix off-by-one error in CheckCollisionPointRec() (#3022) by @dbechrd
[rtextures] ADDED: Basic SVG loading support (#2738) by @bXi
[rtextures] ADDED: Support 16-Bit HDR textures (#3220) by @Not-Nik
[rtextures] ADDED: ExportImageToMemory() by @raysan5
[rtextures] ADDED: ImageRotate() (#3078) by @danemadsen
[rtextures] ADDED: GenImageGradientSquare() (#3077) by @danemadsen
[rtextures] ADDED: GenImageLinearGradient() by @danemadsen
[rtextures] REMOVED: GenImageGradientH() and GenImageGradientV() by @danemadsen
[rtextures] REVIEWED: LoadImageSvg() by @raysan5
[rtextures] REVIEWED: Uninitialized thread-locals in stbi (#3282) (#3283) by @jbarthelmes
[rtextures] REVIEWED: ImageDrawRectangleRec(), validate drawing inside bounds by @JeffM2501
[rtextures] REVIEWED: LoadTextureCubemap() for manual layouts (#3204) by @Not-Nik
[rtextures] REVIEWED: Optimization of ImageDrawRectangleRec() (#3185) by @smalltimewizard
[rtextures] REVIEWED: ImageRotate() formatting by @raysan5
[rtextures] REVIEWED: GenImagePerlinNoise(), clamp values (#3071) by @raysan5
[rtextures] REVIEWED: Packing logic error in GenImageFontAtlas() (#2979) by @hanaxar
[rtextures] REVIEWED: Calculate exact image size in GenImageFontAtlas() (#2963) by @hanaxar
[rtextures] REVIEWED: ImageDrawRectangleRec() (#3027) by @raysan5
[rtextures] REVIEWED: ImageDraw() source clipping when drawing beyond top left (#3306) by @RobLoach
[rtextures] REVIEWED: UnloadRenderTexture(), additional checks
[rtextures] REVIEWED: Fixed compressed DDS texture loading issues (#3483) by @JaanDev
[rtext] ADDED: Font altas white rectangle and flag SUPPORT_FONT_ATLAS_WHITE_REC by @raysan5
[rtext] ADDED: SetTextLineSpacing() to define line breaks text drawing spacing by @raysan5
[rtext] RENAMED: LoadFont*() parameter names for consistency and coherence by @raysan5
[rtext] REVIEWED: GetCodepointCount(), ignore unused return value of GetCodepointNext by @ashn-dot-dev
[rtext] REVIEWED: TextFormat() warn user if buffer overflow occured (#3399) by @Murlocohol
[rtext] REVIEWED: TextFormat(), added "..." for truncation (#3366) by @raysan5
[rtext] REVIEWED: GetGlyphIndex() (#3000) by @raysan5
[rtext] REVIEWED: GetCodepointNext() to return default value by @chocolate42
[rtext] REVIEWED: TextToPascal() issue when first char is uppercase
[rmodels] ADDED: ModelAnimation.name field, initially with GLTF animation names by @alfredbaudisch
[rmodels] REDESIGNED: LoadOBJ(), avoid mesh splitting by materials, fix (#3398) by @raysan5
[rmodels] REVIEWED: Support .vox model files version 200 (#3097) by @Bigfoot71
[rmodels] REVIEWED: Materials loading (#3126) @raysan5
[rmodels] REVIEWED: DrawBillboardPro() to allow source of negative size (#3197) by @bohonghuang
[rmodels] REVIEWED: glTF loading segfault in animNormals memcpy by @charles-l
[rmodels] REVIEWED: LoadModelAnimationsGLTF(), free fileData after use (#3065) by @crynux
[rmodels] REVIEWED: GenMeshCubicmap(), correction of values (#3032) by @Bigfoot71
[rmodels] REVIEWED: DrawMesh() to avoid UBSAN complaining (#1891)
[rmodels] REVIEWED: GenMeshPlane() when resX != resZ (#3425) by @neyrox, @s-yablonskiy
[rmodels] REVIEWED: GetModelBoundingBox() (#3485)
[raudio] ADDED: LoadSoundAlias() by @JeffM2501
[raudio] ADDED: Missing structure on standalone mode (#3160) by @raysan5
[raudio] ADDED: GetMasterVolume() (#3434) by @rexim
[raudio] REVIEWED: Comments about sample format to AttachAudioStreamProcessor() (#3188) by @AlbertoGP
[raudio] REVIEWED: Documented buffer format for audio processors (#3186) by @AlbertoGP
[raudio] REVIEWED: ExportWaveAsCode() file saving by @RadsammyT
[raudio] REVIEWED: Fix warning on discarded const qualifier (#2967) by @RobLoach
[raudio] REVIEWED: Move mutex initialization before ma_device_start() (#3325) by @Bigfoot71
[raudio] REVIEWED: Fix UpdateSound() parameter name (#3405) by @KislyjKisel
[raudio] REVIEWED: Fix QOA seeking (#3494) by @veins1
[rcamera] REVIEWED: File-macros for consistency (#3161) by @raysan5
[rcamera] REVIEWED: Support analog stick camera controls (#3066) by @PixelPhobicGames
[rcamera] REVIEWED: CameraMoveToTarget(), ensure distance is greater than 0 (#3031) by @kolunmi
[rcamera] REVIEWED: Exposing rcamera functions to the dll (#3355) by @JeffM2501
[raymath] ADDED: Vector3Projection() and Vector3Rejection() (#3263) by @Dial0
[raymath] ADDED: EPSILON macro to each function requiring it (#3330) by @Brian-ED
[raymath] REVIEWED: Usage of 'sinf()' and 'cosf()' to be correct (#3181) by @RokasPuzonas
[raymath] REVIEWED: Slightly optimized Vector3Normalize() (#2982) by @RicoP
[raymath] REVIEWED: Comment to clarify raymath semantics by @raysan5
[raymath] REVIEWED: Comment about Matrix conventions by @raysan5
[raymath] REVIEWED: Vector2Angle() and Vector2LineAngle() (#3396) by @Murlocohol
[rgestures] REVIEWED: Optimize and simplify the gesture system (#3190) by @ubkp
[rgestures] REVIEWED: GESTURE_DRAG and GESTURE_SWIPE_* issues (mostly) for web (#3183) by @ubkp
[rgestures] REVIEWED: Touch pointCount for web (#3163) by @ubkp
[rgestures] REVIEWED: IsGestureDetected() parameter type
[utils] ADDED: Security checks to file reading (memory allocations) by @raysan5
[utils] REVIEWED: LoadFileData() potential issues with dataSize
[examples] ADDED: shaders_lightmap (#3043) by @nullstare
[examples] ADDED: core_2d_camera_split_screen (#3298) by @gabrielssanches
[examples] ADDED: LoadSoundAlias() usage example (#3223) by @JeffM2501
[examples] ADDED: textures_tiling (#3353) by @luis605
[examples] ADDED: shader_deferred_render (#3496) by @27justin
[examples] RENAMED: 2d_camera examples for consistency
[examples] REVIEWED: Text examples SetTextLineSpacing() to multiline examples by @raysan5
[examples] REVIEWED: examples/shapes/shapes_collision_area.c help instructions (#3279) by @asdqwe
[examples] REVIEWED: examples/shaders/shaders_texture_outline.c help instructions (#3278) by @asdqwe
[examples] REVIEWED: examples/others/easings_testbed.c help instructions and small twe…  by @asdqwe
[examples] REVIEWED: example/audio/audio_module_player.c help instructions and small b…  by @asdqwe
[examples] REVIEWED: example/models/models_loading_m3d.c controls (#3269) by @asdqwe
[examples] REVIEWED: example/models/models_loading_gltf.c controls (#3268) by @asdqwe
[examples] REVIEWED: text_unicode.c example crashing (#3250) by @ubkp
[examples] REVIEWED: rlgl_standalone.c compilation issue (#3242) by @ubkp
[examples] REVIEWED: core_input_gestures for Web (#3172) by @ubkp
[examples] REVIEWED: core_input_gamepad (#3110) by @iacore
[examples] REVIEWED: examples using raygui to raygui 4.0 by @raysan5
[examples] REVIEWED: Julia set shader example (#3467) by @joshcol9232
[build] ADDED: CMake option for SUPPORT_CUSTOM_FRAME_CONTROL (#3221) by @ubkp
[build] ADDED: New BORDERLESS_WINDOWED_MODE for PLATFORM_DESKTOP (#3216) by @ubkp
[build] ADDED: New examples to VS2022 solution by @raysan5
[build] REVIEWED: Updated Makefile and Makefile.Web, include new examples
[build] REVIEWED: Fix CMake extraneous -lglfw (#3266) by @iacore
[build] REVIEWED: Add missing cmake options (#3267) by @asdqwe
[build] REVIEWED: Match CMakeOptions.txt options default values (#3258) by @asdqwe
[build] REVIEWED: Add build.zig options for individual modules (#3254) by @actondev
[build] REVIEWED: build.zig to work with cross-compiling (#3225) by @yujiri8
[build] REVIEWED: Makefile build on PLATFORM_ANDROID, soname (#3211) by @ndytts
[build] REVIEWED: src/Makefile, fix misleading indentation (#3202) by @ashn-dot-dev
[build] REVIEWED: build.zig: Support for building with PLAFORM_DRM (#3191) by @jakubvf
[build] REVIEWED: Update CMakeOptions.txt by @raysan5
[build] REVIEWED: fix: cmake option "OPENGL_VERSION" doesn't work (#3170) by @royqh1979
[build] REVIEWED: Add error if raylib.h is included in a C++98 program (#3093) by @Peter0x44
[build] REVIEWED: Cross compilation for PLATFORM_DRM (#3091) by @TheLastBilly
[build] REVIEWED: build.zigm fixed cross-compiling from Linux (#3090)by @yujiri8
[build] REVIEWED: Enhanced cmake part for OpenBSD (#3086) by @rayit
[build] REVIEWED: Fixed compile on OpenBSD (#3085)by @rayit
[build] REVIEWED: CMake project example: fix a couple of typos (#3014) by @benjamin-thomas
[build] REVIEWED: Fix warnings in raylib for MSVC (#3004) by @JeffM2501
[build] REVIEWED: Update cmake example project (#3062) by @lesleyrs
[build] REVIEWED: Update build.zig be be able to build with current zig master (#3064) by @ryupold
[build] REVIEWED: VSCode project template (#3048) by @Shoozza
[build] REVIEWED: Fixed broken build.zig files. Now works with latest stable compiler (… by @Gamer-Kold
[build] REVIEWED: Fix missing symbol when rglfw.c on BSD platforms (#2968) by @Koromix
[build] REVIEWED: Update Makefile comment to indicate arm64 as a supported Linux deskto…  @ashn-dot-dev
[build] REVIEWED: Update Makefile : clean raygui.c & physac.c (#3296) by @SuperUserNameMan
[build] REVIEWED: Update webassembly.yml and linux.yml
[build] REVIEWED: Update zig build system to zig version 0.11.0 (#3393) by @purple4pur
[build] REVIEWED: Fix for latest zig master (#3037) by @star-tek-mb
[build] REVIEWED: Examples Makefile to use Makefile.Web when building for web (#3449) by @keithstellyes
[build] REVIEWED: build.zig updates for 0.11.0 release. (#3501) by @cabarger
[build] REVIEWED: Support OpenGL ES 3.0 building on Web platform
[build] REVIEWED: Fix warnings in Visual Studio (#3512) by @JeffM2501
[build] REVIEWED: OpenGL ES 3.0 flags on CMakeOptions (#3514) by @awfulcooking
[bindings] ADDED: fortran-raylib
[bindings] ADDED: raylib-raku to bindings (#3299) by @vushu
[bindings] ADDED: claw-raylib to BINDINGS.md (#3310) by @bohonghuang
[bindings] ADDED: vaiorabbit/raylib-bindings (#3318) by @wilsonsilva
[bindings] ADDED: TurboRaylib (#3317) by @turborium
[bindings] ADDED: raylib-ffi to bindings list (#3164) by @ewpratten
[bindings] ADDED: raylib-pkpy-bindings (#3361) by @blueloveTH
[bindings] ADDED: Raylib.lean to BINDINGS.md (#3409) by @KislyjKisel
[bindings] UPDATED: BINDINGS.md (#3217) by @joseph-montanez
[bindings] UPDATED: BINDINGS.md to include rayjs (#3212) by @mode777
[bindings] UPDATED: latest h-raylib version (#3166) by @Anut-py
[bindings] UPDATED: bindbd-raylib3 to raylib 4.5 (#3157) by @o3o
[bindings] UPDATED: Janet bindings supported version update (#3083)by @archydragon
[bindings] UPDATED: BINDINGS.md (raylib-py -> 4.5) (#2992) by @overdev
[bindings] UPDATED: BINDINGS.md (raylib-lua -> 4.5) (#2989) by @TSnake41
[bindings] UPDATED: raylib-d binding version to 4.5 (#2988) by @schveiguy
[bindings] UPDATED: raylib-freebasic to 4.5 (#2986) by @WIITD
[bindings] UPDATED: BINDINGS.md (#2983) by @jarroddavis68
[bindings] UPDATED: BINDINGS.md for raylib Odin 4.5 (#2981) by @gingerBill
[bindings] UPDATED: BINDINGS.md (#2980) by @GuvaCode
[bindings] UPDATED: BINDINGS.md (#3002) by @fubark
[bindings] UPDATED: BINDINGS.md (#3053) by @JupiterRider
[bindings] UPDATED: BINDINGS.md (#3050) by @Its-Kenta
[bindings] UPDATED: CL bindings version (#3049) by @shelvick
[bindings] UPDATED: BINDINGS.md (#3026) by @ChrisDill
[bindings] UPDATED: BINDINGS.md (#3023) by @sDos280
[bindings] UPDATED: BINDINGS.md (#3017) by @Soutaisei
[bindings] UPDATED: Various versions to 4.5 (#2974) by @RobLoach
[bindings] UPDATED: raylib.zig version to 4.5 (#2971) by @ryupold
[bindings] UPDATED: h-raylib version (#2970) by @Anut-py
[bindings] UPDATED: Factor's raylib binding to v4.5 (#3350) by @WraithGlade
[bindings] UPDATED: raylib-ocaml bindings to 4.5 version (#3322) by @tjammer
[bindings] UPDATED: Jaylib binding (#3508) by @glowiak
[external] UPDATED: sdefl and sinfl DEFLATE compression libraries by @raysan5
[external] UPDATED: miniaudio v0.11.12 --> v0.11.19 by @raysan5
[external] UPDATED: rl_gputex.h compressed images loading library by @raysan5
[external] UPDATED: Replaced stb_image_resize.c by stb_image_resize2.h (#3403) by @BabakSamimi
[external] UPDATED: qoi and qoa libraries
[external] UPDATED: stb libraries (required ones)
[external] UPDATED: cgltf and m3d libraries
[external] REVIEWED: msf_gif.h, some warnings
[external] REVIEWED: sinfl external library to avoid ASAN complaints (#3349) by @raysan5
[misc] ADDED: New task point to issue template about checking the wiki (#3169) by @ubkp
[misc] ADDED: CodeQL for static code analysis (#3476) by @b4yuan
[misc] REVIEWED: Update FAQ.md by @raysan5
[misc] REVIEWED: Potential code issues reported by CodeQL (#3476)
[misc] REVIEWED: Fix a link in the FAQ (#3082)by @jasonliang-dev
[misc] REVIEWED: New file formats to FAQ (#3079) by @Luramoth
[misc] REVIEWED: Make assets loading extension case insensitive #3008 by @raysan5
[misc] REVIEWED: Updated web shells open-graph info by @raysan5

-------------------------------------------------------------------------
Release:     raylib 4.5 (18 March 2023)
-------------------------------------------------------------------------
KEY CHANGES:
 - ADDED: Improved ANGLE support on Desktop platforms
 - ADDED: rcamera module, simpler and more extendable
 - ADDED: Support for M3D models and M3D/GLTF animations
 - ADDED: Support QOA audio format (import/export)
 - ADDED: rl_gputex module for compressed textures loading
 - REDESIGNED: rlgl module for automatic render-batch limits checking
 - REDESIGNED: rshapes module to minimize the rlgl dependency

Detailed changes:
[core] ADDED: RAYLIB_VERSION_* values to raylib.h (#2856) by @RobLoach
[core] ADDED: Basic gamepad support for Android (#2709) by @deniska
[core] ADDED: Support CAPS/NUM lock keys registering if locked
[core] ADDED: _GNU_SOURCE define on Linux (#2729)
[core] ADDED: SetWindowIcons() to set multiple icon image sizes
[core] `WARNING`: RENAMED: Exported raylib version symbol to raylib_version #2671
[core] REMOVED: Touch points on touch up events on Android (#2711) by @deniska
[core] REVIEWED: Window position setup on InitWindow() (#2732) by @RandomErrorMessage
[core] REVIEWED: Touchscreen input related functions on Android (#2702) by @deniska
[core] REVIEWED: Viewport scaling on Android after context rebind (#2703) by @deniska
[core] REVIEWED: ScanDirectoryFilesRecursively() (#2704)
[core] REVIEWED: Gamepad mappings with latest gamecontrollerdb (#2725)
[core] REVIEWED: Monitor order check on app initialization
[core] REVIEWED: Application monitor when opening (#2728, #2731) by @RandomErrorMessage
[core] REVIEWED: Gestures module to use GetTime() if available (#2733) by @RobLoach
[core] REVIEWED: Resolve GLFW3 some symbols re-definition of windows.h in glfw3native (#2643) by @daipom
[core] REVIEWED: OpenURL(), string buffer too short sometimes
[core] REVIEWED: GetRandomValue() range limit warning (#2800) by @Pere001
[core] REVIEWED: UnloadDirectoryFiles()
[core] REVIEWED: GetKeyPressed(), out of range issue (#2814) by @daipom
[core] REVIEWED: GetTime(), renamed variable 'time' to 'nanoSeconds' (#2816) by @jtainer
[core] REVIEWED: LoadShaderFromMemory(), issue with shader linkage
[core] REVIEWED: Avoid possible gamepad index as -1 (#2839)
[core] REVIEWED: SetShaderValue*(), avoid setup uniforms for invalid locations
[core] REVIEWED: GetClipboardText() on PLATFORM_WEB, permissions issues
[core] REVIEWED: Initial window position for display-sized fullscreen (#2742) by @daipom
[core] REVIEWED: Sticky touches input (#2857) by @ImazighenGhost
[core] REVIEWED: Enable GetWindowHandle() on macOS (#2915) by @Not-Nik
[core] REVIEWED: Window position always inits centered in current monitor
[core] REVIEWED: IsWindowFocused() to consider Android App state (#2935)
[core] REVIEWED: GetMonitorWidth() and GetMonitorHeight() (#2934)
[core] REVIEWED: GetWindowHandle() to return Linux window (#2938)
[core] REVIEWED: WindowDropCallback(), additional security check (#2943)
[core] REVIEWED: Security checks for emscripten_run_script() (#2954)
[utils] REVIEWED: TraceLog() message size limit overflow
[rcamera] REDESIGNED: New implementation from scratch (#2563) by @Crydsch
[rcamera] REVIEWED: Make orbital camera work as expected (#2926) by @JeffM2501
[rcamera] REVIEWED: Multiple reviews on the new implementation
[rcamera] ADDED: UpdateCameraPro(), supporting custom user inputs
[rlgl] ADDED: OpenGL ES 2.0 support on PLATFORM_DESKTOP (#2840) by @wtnbgo
[rlgl] ADDED: Separate blending modes for color and alpha, BLEND_CUSTOM_SEPARATE (#2741)
[rlgl] ADDED: rlSetBlendFactorsSeparate and custom blend mode modification checks (#2741) by @pure01fx
[rlgl] ADDED: RL_TEXTURE_MIPMAP_BIAS_RATIO support to `rlTextureParameters()` for OpenGL 3.3 #2674
[rlgl] ADDED: rlCubemapParameters() (#2862) by @GithubPrankster
[rlgl] ADDED: rlSetCullFace() (#2797) by @jtainer
[rlgl] REMOVED: Mipmaps software generation for OpenGL 1.1
[rlgl] REVIEWED: Check for extensions before enabling them (#2706) by @Not-Nik
[rlgl] REVIEWED: SSBO usage to avoid long long data types
[rlgl] REVIEWED: Enable DXT compression on __APPLE__ targets (#2694) by @Not-Nik
[rlgl] REVIEWED: enums exposed and description comments
[rlgl] REVIEWED: rlBindImageTexture(), correct data types (#2808) by @planetis-m
[rlgl] REVIEWED: rlMultMatrixf(), use const pointer (#2807) by @planetis-m
[rlgl] REVIEWED: Expose OpenGL blending mode factors and functions/equations
[rlgl] REVIEWED: rLoadTextureDepth(), issue with depth textures on WebGL (#2824)
[rlgl] REVIEWED: rlUnloadFramebuffer() (#2937)
[raymath] ADDED: Vector2LineAngle() (#2887)
[raymath] REVIEWED: Vector2Angle() (#2829, #2832) by @AlxHnr, @planetis-m
[shapes] ADDED: CheckCollisionPointPoly() (#2685) by @acejacek
[shapes] REVIEWED: DrawPixel*(), use RL_QUADS/RL_TRIANGLES (#2750) by @hatkidchan
[shapes] REVIEWED: DrawLineBezier*(), fix bezier line breaking (#2735, #2767) by @nobytesgiven
[textures] ADDED: ColorBrightness()
[textures] ADDED: ColorTint()
[textures] ADDED: ColorContrast()
[textures] ADDED: Support for PNM images (.ppm, .pgm)
[textures] ADDED: GenImagePerlinNoise()
[textures] ADDED: GenImageText(), generate grayscale image from text byte data
[textures] ADDED: ImageDrawCircleLines(), ImageDrawCircleLinesV() (#2713) by @RobLoach
[textures] ADDED: ImageBlurGaussian() (#2770) by @nobytesgiven
[textures] REVIEWED: Image fileformat support: PIC, PNM
[textures] REVIEWED: ImageTextEx() and ImageDrawTextEx() scaling (#2756) by @hatkidchan
[textures] `WARNING`: REMOVED: DrawTextureQuad()
[textures] `WARNING`: REMOVED: DrawTexturePoly(), function moved to example: `textures_polygon`
[textures] `WARNING`: REMOVED: DrawTextureTiled(),function implementation moved to the textures_tiled.c
[text] ADDED: GetCodepointPrevious()
[text] ADDED: UnloadUTF8(), aligned with LoadUTF8()
[text] `WARNING`: RENAMED: TextCodepointsToUTF8() to LoadUTF8()
[text] `WARNING`: RENAMED: GetCodepoint() -> GetCodepointNext()
[text] REDESIGNED: GetCodepointNext()
[text] REVIEWED: MeasureTextEx(), avoid crash on bad data
[text] REVIEWED: UnloadFontData(), avoid crash on invalid font data
[models] ADDED: Support M3D model file format (meshes and materials) (#2648) by @bztsrc
[models] ADDED: Support for M3D animations (#2648) by @bztsrc
[models] ADDED: GLTF animation support (#2844) by @charles-l
[models] ADDED: DrawCapsule() and DrawCapsuleWires() (#2761) by @IanBand
[models] ADDED: LoadMaterials(), MTL files loading, same code as OBJ loader (#2872) by @JeffM2501
[models] `WARNING`: REMOVED: UnloadModelKeepMeshes()
[models] `WARNING`: REMOVED: DrawCubeTexture(), DrawCubeTextureRec(), functions moved to new example: `models_draw_cube_texture`
[models] REVIEWED: DrawMesh(), using SHADER_LOC_COLOR_SPECULAR as a material map (#2908) by @haved
[models] REVIEWED: LoadM3D() vertex color support (#2878) by @GithubPrankster, @bztsrc
[models] REVIEWED: GenMeshHeightmap() (#2716)
[models] REVIEWED: LoadIQM() (#2676)
[models] REVIEWED: Simplify .vox signature check (#2752) by @CrezyDud
[models] REVIEWED: LoadIQM(), support bone names loading if available (#2882) by @PencilAmazing
[models] REVIEWED: GenMeshTangents(), avoid crash on missing texcoords data (#2927)
[audio] ADDED: Full support for QOA audio file format
[audio] ADDED: Mixed audio processor (#2929) by @hatkidchan
[audio] ADDED: IsWaveReady()`, IsSoundReady(), IsMusicReady() (#2892) by @RobLoach
[audio] `WARNING`: REMOVED: Multichannel audio API: PlaySoundMulti(), StopSoundMulti()
[audio] REVIEWED: Clear PCM buffer state when closing audio device (#2736) by @veins1
[audio] REVIEWED: Android backend selected (#2118, #2875) by @planetis-m
[audio] REVIEWED: Change default threading model for COM objects in miniaudio
[multi] ADDED: IsShaderReady(), IsImageReady(), IsFontReady() (#2892) by @RobLoach
[multi] ADDED: IsModelReady(), IsMaterialReady(), IsTextureReady(), IsRenderTextureReady() (#2895) by @RobLoach
[multi] REVIEWED: Multiple code/comment typos by @sDos280
[multi] REVIEWED: Grammar mistakes and typos (#2914) by @stickM4N
[multi] REVIEWED: Use TRACELOG() macro instead of TraceLog() in internal modules (#2881) by @RobLoach
[examples] ADDED: textures_textured_curve (#2821) by @JeffM2501
[examples] ADDED: models_draw_cube_texture
[examples] ADDED: models_loading_m3d (#2648) by @bztsrc
[examples] ADDED: shaders_write_depth (#2836) by @BugraAlptekinSari
[examples] ADDED: shaders_hybrid_render (#2919) by @BugraAlptekinSari
[examples] REMOVED: audio_multichannel_sound
[examples] RENAMED: Several shaders for naming consistency (#2707)
[examples] RENAMED: lighting_instanced.fs to lighting_instancing.fs (glsl100) (#2805) by @gtrxAC
[examples] REVIEWED: core_custom_logging.c (#2692) by @hartmannathan
[examples] REVIEWED: core_camera_2d_platformer (#2687) by @skylar779
[examples] REVIEWED: core_input_gamepad.c (#2903) by @planetis-m
[examples] REVIEWED: core_custom_frame_control
[examples] REVIEWED: core_drop_files (#2943)
[examples] REVIEWED: text_rectangle_bounds (#2746) by @SzieberthAdam
[examples] REVIEWED: textures_image_processing, added gaussian blurring (#2775) by @nobytesgiven
[examples] REVIEWED: models_billboard, highlighting rotation and draw order (#2779) by @nobytesgiven
[examples] REVIEWED: core_loading_thread, join thread on completion (#2845) by @planetis-m
[examples] REVIEWED: models_loading_gltf
[examples] REVIEWED: Shader lighting.fs for GLSL120 (#2651)
[examples] REVIEWED: text_codepoints_loading.c
[parser] REVIEWED: raylib-parser Makefile (#2765) by @Peter0x44
[build] ADDED: Packaging for distros with deb-based and rpm-based packages (#2877) by @KOLANICH
[build] ADDED: Linkage library -latomic on Linux (only required for ARM32)
[build] ADDED: Required frameworks on macOS (#2793) by @SpexGuy
[build] ADDED: WASM support for Zig build (#2901) by @Not-Nik
[build] ADDED: New raylib examples as VS2022 project (to raylib solution)
[build] REVIEWED: config.h format and inconsistencies
[build] REVIEWED: Zig build to latest master, avoid deprecated functions (#2910) by @star-tek-mb
[build] REVIEWED: CMake project template to easily target raylib version (#2700) by @RobLoach
[build] REVIEWED: PATH for PLATFORM_WEB target (#2647) by @futureapricot
[build] REVIEWED: build.zig to let user decide how to set build mode and linker fixes by @InKryption
[build] REVIEWED: Deprecation error on Android API higher than 23 (#2778) by @anggape
[build] REVIEWED: Android x86 Architecture name (#2783) by @IsaacTCB
[build] REVIEWED: examples/build.zig for the latest Zig version (#2786) by @RomanAkberov
[utils] REVIEWED: ExportDataAsCode() data types (#2787) by @RGDTAB
[build] REVIEWED: Makefile emscripten path (#2785) by @Julianiolo
[build] REVIEWED: Several compilation warnings (for strict rules)
[build] REVIEWED: All github workflows using deprecated actions
[build] REVIEWED: CMake when compiling for web (#2820) by @object71
[build] REVIEWED: DLL build on Windows (#2951) by @Skaytacium
[build] REVIEWED: Avoid MSVC warnings in raylib project (#2871) by @JeffM2501
[build] REVIEWED: Paths in .bat files to build examples (#2870) by @masoudd
[build] REVIEWED: CMake, use GLVND for old cmake versions (#2826) by @simendsjo
[build] REVIEWED: Makefile, multiple tweaks
[build] REVIEWED: CI action: linux_examples.yml
[build] REVIEWED: CI action: cmake.yml
[bindings] ADDED: h-raylib (Haskell) by @Anut-py
[bindings] ADDED: raylib-c3 (C3) by @Its-Kenta
[bindings] ADDED: raylib-umka (Umka) by @RobLoach
[bindings] ADDED: chez-raylib (Chez Scheme) by @Yunoinsky
[bindings] ADDED: raylib-python-ctypes (Python) by @sDos280
[bindings] ADDED: claylib (Common Lisp) by @shelvick
[bindings] ADDED: raylib-vapi (Vala) by @lxmcf
[bindings] ADDED: TurboRaylib (Object Pascal) by @turborium
[bindings] ADDED: Kaylib (Kotlin/Native) by @Its-Kenta
[bindings] ADDED: Raylib-Nelua (Nelua) by @Its-Kenta
[bindings] ADDED: Cyber (Cyber) by @fubark
[bindings] ADDED: raylib-sunder (Sunder) by @ashn-dot-dev
[bindings] ADDED: raylib BQN (#2962) by @Brian-ED
[misc] REVIEWED: Update external libraries to latest versions

-------------------------------------------------------------------------
Release:     raylib 4.2 (11 August 2022)
-------------------------------------------------------------------------
KEY CHANGES:
 - REMOVED: extras libraries (raygui, physac, rrem, reasings, raudio.h) moved to independent separate repos
 - UPDATED: examples: Added creation and update raylib versions and assigned **DIFFICULTY LEVELS**!
 - rres 1.0: A custom resource-processing and packaging file format, including tooling and raylib integration examples
 - raygui 3.2: New version of the immediate-mode gui system for tools development with raylib
 - raylib_parser: Multiple improvements of the raylib parser to automatize bindings generation
 - ADDED: New file system API: Reviewed to be more aligned with raylib conventions and one advance function added
 - ADDED: New audio stream processors API (_experimental_): Allowing to add custom audio stream data processors using callbacks

Detailed changes:
[multi] ADDED: Frequently Asked Questions (FAQ.md)
[multi] REVIEWED: Multiple trace log messages
[multi] REVIEWED: Avoid some float to double promotions
[multi] REVIEWED: Some functions input parametes that should be const
[multi] REVIEWED: Variables initialization, all variables are initialized on declaration
[multi] REVIEWED: Static array buffers are always re-initialized with memset()
[multi] `WARNING`: RENAMED: Some function input parameters from "length" to "size"
[core] ADDED: GetApplicatonDirectory() (#2256, #2285, #2290) by @JeffM2501
[core] ADDED: raylibVersion symbol, it could be required by some bindings (#2190)
[core] ADDED: SetWindowOpacity() (#2254) by @tusharsingh09
[core] ADDED: GetRenderWidth() and GetRenderHeight() by @ArnaudValensi
[core] ADDED: EnableEventWaiting() and DisableEventWaiting()
[core] ADDED: GetFileLength()
[core] ADDED: Modules info at initialization
[core] ADDED: Support clipboard copy/paste on web
[core] ADDED: Support OpenURL() on Android platform (#2396) by @futureapricot
[core] ADDED: Support MOUSE_PASSTHROUGH (#2516)
[core] ADDED: GetMouseWheelMoveV() (#2517) by @schveiguy
[core] `WARNING`: REMOVED: LoadStorageValue() / SaveStorageValue(), moved to example
[core] `WARNING`: RENAMED: GetDirectoryFiles() to LoadDirectoryFiles()
[core] `WARNING`: RENAMED: `ClearDroppedFiles()` to `UnloadDroppedFiles()`
[core] `WARNING`: RENAMED: GetDroppedFiles() to LoadDroppedFiles()
[core] `WARNING`: RENAMED: `ClearDirectoryFiles()` to `UnloadDirectoryFiles()`
[core] `WARNING`: REDESIGNED: WaitTime() argument from milliseconds to seconds (#2506) by @flashback-fx
[core] REVIEWED: GetMonitorWidth()/GetMonitorHeight() by @gulrak
[core] REVIEWED: GetDirectoryFiles(), maximum files allocation (#2126) by @ampers0x26
[core] REVIEWED: Expose MAX_KEYBOARD_KEYS and MAX_MOUSE_BUTTONS (#2127)
[core] REVIEWED: ExportMesh() (#2138)
[core] REVIEWED: Fullscreen switch on PLATFORM_WEB
[core] REVIEWED: GetMouseWheelMove(), fixed bug
[core] REVIEWED: GetApplicationDirectory() on macOS (#2304)
[core] REVIEWED: ToggleFullscreen()
[core] REVIEWED: Initialize/reset CORE.inputs global state (#2360)
[core] REVIEWED: MouseScrollCallback() (#2371)
[core] REVIEWED: SwapScreenBuffers() for PLATFORM_DRM
[core] REVIEWED: WaitTime(), fix regression causing video stuttering (#2503) by @flashback-fx
[core] REVIEWED: Mouse device support on PLATFORM_DRM (#2381)
[core] REVIEWED: Support OpenBSD timming functions
[core] REVIEWED: Improved boolean definitions (#2485) by @noodlecollie
[core] REVIEWED: TakeScreenshot(), use GetWindowScaleDPI() to calculate size in screenshot/recording (#2446) by @gulrak
[core] REVIEWED: Remove fps requirement for drm connector selection (#2468) by @Crydsch
[core] REVIEWED: IsFileExtension() (#2530)
[camera] REVIEWED: Some camera improvements (#2563)
[rlgl] ADDED: Premultiplied alpha blend mode (#2342) by @megagrump
[rlgl] REVIEWED: VR rendering not taking render target size into account (#2424) by @FireFlyForLife
[rlgl] REVIEWED: Set rlgl internal framebuffer (#2420)
[rlgl] REVIEWED: rlGetCompressedFormatName()
[rlgl] REVIEWED: Display OpenGL 4.3 capabilities with a compile flag (#2124) by @GithubPrankster
[rlgl] REVIEWED: rlUpdateTexture()
[rlgl] REVIEWED: Minimize buffer overflow probability
[rlgl] REVIEWED: Fix scissor mode on macOS (#2170) by @ArnaudValensi
[rlgl] REVIEWED: Clear SSBO buffers on loading (#2185)
[rlgl] REVIEWED: rlLoadShaderCode(), improved shader loading code
[rlgl] REVIEWED: Comment notes about custom blend modes (#2260) by @glorantq
[rlgl] REVIEWED: rlGenTextureMipmaps()
[rlgl] REVIEWED: rlTextureParameters()
[raymath] ADDED: Wrap() (#2522) by @Tekkitslime
[raymath] ADDED: Vector2Transform()
[raymath] ADDED: Vector2DistanceSqr() (#2376) by @AnilBK
[raymath] ADDED: Vector3DistanceSqr() (#2376) by @AnilBK
[raymath] ADDED: Vector2ClampValue(), Vector3ClampValue() (#2428) by @saccharineboi
[raymath] ADDED: Vector3RotateByAxisAngle() (#2590) by @Crydsch
[raymath] `WARNING`: REDESIGNED: Vector2Angle() returns radians instead of degrees (#2193) by @schveiguy
[raymath] `WARNING`: REMOVED: MatrixNormalize() (#2412)
[raymath] REVIEWED: Fix inverse length in Vector2Normalize() (#2189) by @HarriP
[raymath] REVIEWED: Vector2Angle() not working as expected (#2196) by @jdeokkim
[raymath] REVIEWED: Vector2Angle() and Vector3Angle() (#2203) by @trikko
[raymath] REVIEWED: QuaternionInvert(), code simplified (#2324) by @megagrump
[raymath] REVIEWED: QuaternionScale() (#2419) by @tana
[raymath] REVIEWED: Vector2Rotate(), optimized (#2340) by @jdeokkim
[raymath] REVIEWED: QuaternionFromMatrix(), QuaternionEquals() (#2591) by @kirigirihitomi
[raymath] REVIEWED: MatrixRotate*() (#2595, #2599) by @GoodNike
[shapes] REVIEWED: CheckCollision*() consistency
[shapes] REVIEWED: DrawRectanglePro(), support TRIANGLES drawing
[textures] ADDED: Support for QOI image format
[textures] REVIEWED: ImageColorTint(), GetImageColor(), ImageDrawRectangleRec(), optimized functions (#2429) by @AnilBK
[textures] REVIEWED: LoadTextureFromImage(), allow texture loading with no data transfer
[textures] REVIEWED: ImageDraw(), comment to note that f32bit is not supported (#2222)
[textures] REVIEWED: DrawTextureNPatch(), avoid batch overflow (#2401) by @JeffM2501
[textures] REVIEWED: DrawTextureTiled() (#2173)
[textures] REVIEWED: GenImageCellular() (#2178)
[textures] REVIEWED: LoadTextureCubemap() (#2223, #2224)
[textures] REVIEWED: Export format for float 32bit
[textures] REVIEWED: ExportImage(), support export ".jpeg" files
[textures] REVIEWED: ColorAlphaBlend() (#2524) by @royqh1979
[textures] REVIEWED: ImageResize() (#2572)
[textures] REVIEWED: ImageFromImage() (#2594) by @wiertek
[text] ADDED: ExportFontAsCode()
[text] ADDED: DrawTextCodepoints() (#2308) by @siddharthroy12
[text] REVIEWED: TextIsEqual(), protect from NULLs (#2121) by @lukekras
[text] REVIEWED: LoadFontEx(), comment to specify how to get the default character set (#2221) by @JeffM2501
[text] REVIEWED: GenImageFontAtlas(), increase atlas size guesstimate by @megagrump
[text] REVIEWED: GetCodepoint() (#2201)
[text] REVIEWED: GenImageFontAtlas() (#2556)
[text] REVIEWED: ExportFontAsCode() to use given font padding (#2525) by @TheTophatDemon
[models] ADDED: Reference code to load bones id and weight data for animations
[models] `WARNING`: REMOVED: GetRayCollisionModel() (#2405)
[models] REMOVED: GenMeshBinormals()
[models] REVIEWED: External library: vox_loader.h, 64bit issue (#2186)
[models] REVIEWED: Material color loading when no texture material is available (#2298) by @royqh1979
[models] REVIEWED: Fix Undefined Symbol _ftelli64 in cgltf (#2319) by @audinue
[models] REVIEWED: LoadGLTF(), fix memory leak (#2441, #2442) by @leomonta
[models] REVIEWED: DrawTriangle3D() batch limits check (#2489)
[models] REVIEWED: DrawBillboardPro() (#2494)
[models] REVIEWED: DrawMesh*() issue (#2211)
[models] REVIEWED: ExportMesh() (#2220)
[models] REVIEWED: GenMeshCylinder() (#2225)
[audio] `WARNING`: ADDED: rAudioProcessor pointer to AudioStream struct (used by Sound and Music structs)
[audio] ADDED: SetSoundPan(), SetMusicPan(), SetAudioStreamPan(), panning support (#2205) by ptarabbia
[audio] ADDED: Audio stream input callback (#2212) by ptarabbia
[audio] ADDED: Audio stream processors support (#2212) by ptarabbia
[audio] REVIEWED: GetMusicTimePlayed(), incorrect value after the stream restarted for XM audio (#2092 #2215) by @ptarabbia
[audio] REVIEWED: Turn on interpolation for XM playback (#2216) by @ptarabbia
[audio] REVIEWED: Fix crash with delay example (#2472) by @ptarabbia
[audio] REVIEWED: PlaySoundMulti() (#2231)
[audio] REVIEWED: ExportWaveAsCode()
[audio] REVIEWED: UpdateMusicStream(), reduce dynamic allocations (#2532) by @dbechrd
[audio] REVIEWED: UpdateMusicStream() to support proper stream looping (#2579) by @veins1
[utils] ADDED: ExportDataAsCode()
[utils] REVIEWED: Force flush stdout after trace messages (#2465) by @nagy
[easings] ADDED: Function descriptions (#2471) by @RobLoach
[camera] REVIEWED: Fix free camera panning in the wrong direction (#2347) by @DavidLyhedDanielsson
[examples] ADDED: core_window_should_close
[examples] ADDED: core_2d_camera_mouse_zoom (#2583) by @JeffM2501
[examples] ADDED: shapes_top_down_lights (#2199) by @JeffM2501
[examples] ADDED: textures_fog_of_war
[examples] ADDED: textures_gif_player
[examples] ADDED: text_codepoints_loading
[examples] ADDED: audio_stream_effects
[examples] REMOVED: core_quat_conversion, not working properly
[examples] REMOVED: raudio_standalone, moved to raudio repo
[examples] RENAMED: textures_rectangle -> textures_sprite_anim
[examples] REVIEWED: core_input_gamepad, improve joystick visualisation (#2390) by @kristianlm
[examples] REVIEWED: textures_draw_tiled
[examples] REVIEWED: shaders_mesh_instancing, free allocated matrices (#2425) by @AnilBK
[examples] REVIEWED: shaders_raymarching
[examples] REVIEWED: audio_raw_stream (#2205) by ptarabbia
[examples] REVIEWED: audio_music_stream
[examples] REVIEWED: shaders_mesh_instancing, simplified
[examples] REVIEWED: shaders_basic_lighting, rlights.h simplified
[examples] REVIEWED: All examples descriptions, included creation/update raylib versions
[parser] ADDED: Defines to parser (#2269) by @iskolbin
[parser] ADDED: Aliases to parser (#2444) by @lazaray
[parser] ADDED: Parse struct descriptions (#2214) by @eutro
[parser] ADDED: Parse enum descriptions and value descriptions (#2208) by @eutro
[parser] ADDED: Lua output format for parser by @iskolbin
[parser] ADDED: Makefile for raylib_parser by @iskolbin
[parser] ADDED: Support for truncating parser input (#2464) by @lazaray
[parser] ADDED: Support for calculated defines to parser (#2463) by @lazaray
[parser] REVIEWED: Update parser files (#2125) by @catmanl
[parser] REVIEWED: Fix memory leak in parser (#2136) by @ronnieholm
[parser] REVIEWED: EscapeBackslashes()
[parser] REVIEWED: Parser improvements (#2461 #2462) by @lazaray
[bindings] ADDED: License details for BINDINGS
[bindings] ADDED: dart-raylib (#2149) by @wolfenrain
[bindings] ADDED: raylib-cslo (#2169) by @jasonswearingen
[bindings] ADDED: raylib-d (#2194) by @schveiguy
[bindings] ADDED: raylib-guile (#2202) by @petelliott
[bindings] ADDED: raylib-scopes (#2238) by @salotz
[bindings] ADDED: naylib (Nim) (#2386) by @planetis-m
[bindings] ADDED: raylib.jl (Julia) (#2403) by @irishgreencitrus
[bindings] ADDED: raylib.zig (#2449) by @ryupold
[bindings] ADDED: racket-raylib (#2454) by @eutro
[bindings] ADDED: raylibr (#2611) by @ramiromagno
[bindings] ADDED: Raylib.4.0.Pascal (#2617) by @sysrpl
[bindings] REVIEWED: Multiple bindings updated to raylib 4.0
[build] ADDED: VS2022 project
[build] ADDED: Support macOS by zig build system (#2175)
[build] ADDED: Support custom modules selection on compilation
[build] ADDED: Minimal web shell for WebAssembly compilation
[build] ADDED: BSD support for zig builds (#2332) by @zigster64
[build] ADDED: Repology badge (#2367) by @jubalh
[build] ADDED: Support DLL compilation with TCC compiler (#2569) by @audinue
[build] ADDED: Missing examples to VS2022 examples solution
[build] REMOVED: VS2019 project (unmaintained)
[build] REMOVED: SUPPORT_MOUSE_CURSOR_POINT config option
[build] REVIEWED: Fixed RPi make install (#2217) by @wereii
[build] REVIEWED: Fix build results path on Linux and RPi (#2218) by @wereii
[build] REVIEWED: Makefiles debug flag
[build] REVIEWED: Fixed cross-compilation from x86-64 to RPi (#2233) by @pitpit
[build] REVIEWED: All Makefiles, simplified
[build] REVIEWED: All Makefiles, improve organization
[build] REVIEWED: All Makefiles, support CUSTOM_CFLAGS
[build] REVIEWED: Fixed compiling for Android using CMake (#2270) by @hero2002
[build] REVIEWED: Make zig build functionality available to zig programs (#2271) by @Not-Nik
[build] REVIEWED: Update CMake project template with docs and web (#2274) by @RobLoach
[build] REVIEWED: Update VSCode project to work with latest makefile and web (#2296) by @phil-shenk
[build] REVIEWED: Support audio examples compilation with external glfw (#2329) by @locriacyber
[build] REVIEWED: Fix "make clean" target failing when shell is not cmd (#2338) by @Peter0x44
[build] REVIEWED: Makefile linkage -latomic, required by miniaudio on ARM 32bit #2452
[build] REVIEWED: Update raylib-config.cmake (#2374) by @marcogmaia
[build] REVIEWED: Simplify build.zig to not require user to specify raylib path (#2383) by @Hejsil
[build] REVIEWED: Fix OpenGL 4.3 graphics option in CMake (#2427) by @GoldenThumbs
[extras] `WARNING`: REMOVED: physac from raylib sources/examples, use github.com/raysan5/physac
[extras] `WARNING`: REMOVED: raygui from raylib/src/extras, use github.com/raysan5/raygui
[extras] `WARNING`: REMOVED: rmem from raylib/src/extras, moved to github.com/raylib-extras/rmem
[extras] `WARNING`: REMOVED: easings from raylib/src/extras, moved to github.com/raylib-extras/reasings
[extras] `WARNING`: REMOVED: raudio.h from raylib/src, moved to github.com/raysan5/raudio
[misc] REVIEWED: Update some external libraries to latest versions

-------------------------------------------------------------------------
Release:     raylib 4.0 - 8th Anniversary Edition (05 November 2021)
-------------------------------------------------------------------------
KEY CHANGES:
 - Naming consistency and coherency: Complete review of the library: syntax, naming, comments, decriptions, logs...
 - Event Automation System: Support for input events recording and automatic re-playing, useful for automated testing and more!
 - Custom game-loop control: Intended for advanced users that want to control the events polling and the timming mechanisms
 - rlgl 4.0: Completely decoupling from platform layer and raylib, intended for standalone usage as single-file header-only
 - raymath 1.5: Complete review following new conventions, to make it more portable and self-contained
 - raygui 3.0: Complete review and official new release, more portable and self-contained, intended for tools development
 - raylib_parser: New tool to parse raylib.h and extract all required info into custom output formats (TXT, XML, JSON...)
 - Zig and Odin official support

Detailed changes:
[core] ADDED: Support canvas resizing on web (#1840) by @skylersaleh
[core] ADDED: GetMouseDelta() (#1832) by @adricoin2010
[core] ADDED: Support additional mouse buttons (#1753) by @lambertwang
[core] ADDED: SetRandomSeed() (#1994) by @TommiSinivuo
[core] ADDED: GetTouchPointId() #1972
[core] ADDED: EncodeDataBase64() and DecodeDataBase64()
[core] REMOVED: PLATFORM_UWP, difficult to maintain
[core] REMOVED: IsGamepadName()
[core] RENAMED: SwapBuffers() to SwapScreenBuffer()
[core] RENAMED: Wait() to WaitTime()
[core] RENAMED: RayHitInfo to RayCollision (#1781)
[core] RENAMED: GetRayCollisionGround() to GetRayCollisionQuad() (#1781)
[core] REVIEWED: Support mouse wheel on x-axis (#1948)
[core] REVIEWED: DisableCursor() on web by registering an empty mouse click event function in emscripten (#1900) by @grenappels
[core] REVIEWED: LoadShader() and default locations and descriptions
[core] REVIEWED: LoadShaderFromMemory() (#1851) by @Ruminant
[core] REVIEWED: WaitTime(), avoid global variables dependency to make the function is self-contained (#1841)
[core] REVIEWED: SetWindowSize() to work on web (#1847) by @nikki93
[core] REVIEWED: Raspberry RPI/DRM keyboard blocking render loop (#1879) @luizpestana
[core] REVIEWED: Android multi-touch (#1869) by @humbe
[core] REVIEWED: Implemented GetGamepadName() for emscripten by @nbarkhina
[core] REVIEWED: HighDPI support (#1987) by @ArnaudValensi
[core] REVIEWED: KeyCallback(), register keys independently of the actions
[rlgl] ADDED: GRAPHIC_API_OPENGL_43
[rlgl] ADDED: rlUpdateVertexBufferElements() (#1915)
[rlgl] ADDED: rlActiveDrawBuffers() (#1911)
[rlgl] ADDED: rlEnableColorBlend()/rlDisableColorBlend()
[rlgl] ADDED: rlGetPixelFormatName()
[rlgl] REVIEWED: rlUpdateVertexBuffer (#1914) by @630Studios
[rlgl] REVIEWED: rlDrawVertexArrayElements() (#1891)
[rlgl] REVIEWED: Wrong normal matrix calculation (#1870)
[raymath] ADDED: Vector3Angle()
[raymath] REVIEWED: QuaternionFromAxisAngle() (#1892)
[raymath] REVIEWED: QuaternionToMatrix() returning transposed result. (#1793) by @object71
[shapes] ADDED: RenderPolyLinesEx() (#1758) by @lambertwang
[shapes] ADDED: DrawSplineBezierCubic() (#2021) by @SAOMDVN
[textures] ADDED: GetImageColor() #2024
[textures] REMOVED: GenImagePerlinNoise()
[textures] RENAMED: GetTextureData() to LoadImageFromTexture()
[textures] RENAMED: GetScreenData() to LoadImageFromScreen()
[textures] REVIEWED: ExportImage() to use SaveFileData() (#1779)
[textures] REVIEWED: LoadImageAnim() #2005
[text] ADDED: Security check in case of not valid font
[text] ADDED: `GetGlyphInfo()` to get glyph info for a specific codepoint
[text] ADDED: `GetGlyphAtlasRec()` to get glyph rectangle within the generated font atlas
[text] ADDED: DrawTextPro() with text rotation support, WARNING: DrawTextPro() requires including `rlgl.h`, before it was only dependant on `textures` module.
[text] ADDED: UnloadCodepoints() to safely free loaded codepoints
[text] REMOVED: DrawTextRec() and DrawTextRecEx(), moved to example, those functions could be very specific depending on user needs so it's better to give the user the full source in case of special requirements instead of allowing a function with +10 input parameters.
[text] RENAMED: struct `CharInfo` to `GlyphInfo`, actually that's the correct naming for the data contained. It contains the character glyph metrics and the glyph image; in the past it also contained rectangle within the font atlas but that data has been moved to `Font` struct directly, so, `GlyphInfo` is a more correct name.
[text] RENAMED: `CodepointToUtf8()` to `CodepointToUTF8()`, capitalization of UTF-8 is the correct form, it would also require de hyphen but it can be omitted in this case.
[text] RENAMED: `TextToUtf8()` to `TextCodepointsToUTF8` for consistency and more detail on the functionality.
[text] RENAMED: GetCodepoints() to LoadCodepoints(), now codepoint array data is loaded dynamically instead of reusing a limited static buffer.
[text] RENAMED: GetNextCodepoint() to GetCodepoint()
[models] ADDED: MagikaVoxel VOX models loading
[models] ADDED: GenMeshCone() (#1903)
[models] ADDED: GetModelBoundingBox()
[models] ADDED: DrawBillboardPro() (#1759) by @nobytesgiven
[models] ADDED: DrawCubeTextureRec() (#2001) by @tdgroot
[models] ADDED: DrawCylinderEx() and DrawCylinderWiresEx() (#2049) by @Horrowind
[models] REMOVED: DrawBillboardEx()
[models] RENAMED: MeshBoundingBox() to GetMeshBoundingBox()
[models] RENAMED: MeshTangents() to GenMeshTangents()
[models] RENAMED: MeshBinormals() to GenMeshBinormals()
[models] REVIEWED: GenMeshTangents() (#1877) by @630Studios
[models] REVIEWED: CheckCollisionBoxSphere() by @Crydsch
[models] REVIEWED: GetRayCollisionQuad() by @Crydsch
[models] REVIEWED: LoadGLTF(), fixed missing transformations and nonroot skinning by @MrDiver
[models] REVIEWED: LoadGLTF(), rewriten from scratch, removed animations support (broken)
[models] REVIEWED: Decouple DrawMesh() and DrawMeshInstanced() (#1958)
[models] REVIEWED: Support vertex color attribute for GLTF and IQM (#1790) by @object71
[models] REVIEWED: DrawBillboardPro() (#1941) by @GithubPrankster
[models] REDESIGNED: Major review of glTF loading functionality (#1849) by @object71
[audio] ADDED: SeekMusicStream() (#2006) by @GithubPrankster
[audio] REMOVED: GetAudioStreamBufferSizeDefault()
[audio] RENAMED: InitAudioStream() to LoadAudioStream()
[audio] RENAMED: CloseAudioStream() to UnloadAudioStream()
[audio] RENAMED: IsMusicPlaying() to IsMusicStreamPlaying()
[audio] REVIEWED: ExportWaveAsCode()
[audio] REDESIGNED: Use frameCount on audio instead of sampleCount
[utils] REVIEWED: exit() on LOG_FATAL instead of LOG_ERROR (#1796)
[examples] ADDED: core_custom_frame_control
[examples] ADDED: core_basic_screen_manager
[examples] ADDED: core_split_screen (#1806) by @JeffM2501
[examples] ADDED: core_smooth_pixelperfect (#1771) by @NotManyIdeasDev
[examples] ADDED: shaders_texture_outline (#1883) by @GoldenThumbs
[examples] ADDED: models_loading_vox (#1940) by @procfxgen
[examples] ADDED: rlgl_compute_shader by @TSnake41 (#2088)
[examples] REMOVED: models_material_pbr
[examples] REMOVED: models_gltf_animation
[examples] REVIEWED: core_3d_picking
[examples] REVIEWED: core_input_mouse
[examples] REVIEWED: core_vr_simulator, RenderTexture usage
[examples] REVIEWED: core_window_letterbox, RenderTexture usage
[examples] REVIEWED: shapes_basic_shapes
[examples] REVIEWED: shapes_logo_raylib_anim
[examples] REVIEWED: textures_to_image
[examples] REVIEWED: text_rectangle_bounds
[examples] REVIEWED: text_unicode
[examples] REVIEWED: text_draw_3d
[examples] REVIEWED: models_loading
[examples] REVIEWED: models_skybox (#1792) (#1778)
[examples] REVIEWED: models_mesh_picking
[examples] REVIEWED: models_yaw_pitch_roll
[examples] REVIEWED: models_rlgl_solar_system
[examples] REVIEWED: shaders_custom_uniform, RenderTexture usage
[examples] REVIEWED: shaders_eratosthenes, RenderTexture usage
[examples] REVIEWED: shaders_julia_set, RenderTexture usage
[examples] REVIEWED: shaders_postprocessing, RenderTexture usage
[examples] REVIEWED: shaders_basic_lighting, simplified (#1865)
[examples] REVIEWED: audio_raw_stream.c
[examples] REVIEWED: raudio_standalone
[examples] REVIEWED: raylib_opengl_interop
[examples] REVIEWED: rlgl_standalone.c
[examples] REVIEWED: Resources licenses
[examples] REVIEWED: models resources reorganization
[templates] REMOVED: Moved to a separate repo: https://github.com/raysan5/raylib-game-template
[build] ADDED: Zig build file (#2014) by @TommiSinivuo
[build] ADDED: Android VS2019 solution (#2013) by @Kronka
[build] REMOVED: VS2017 project, outdated
[build] RENAMED: All raylib modules prefixed with 'r' (core -> rcore)
[build] RENAMED: SUPPORT_MOUSE_CURSOR_NATIVE to SUPPORT_MOUSE_CURSOR_POINT
[build] REVIEWED: examples/examples_template.c
[build] REVIEWED: Makefile to latest Emscripten SDK r23
[build] REVIEWED: Makefile for latest Android NDK r32 LTS
[build] REVIEWED: raylib resource files
[build] Moved some extra raylib libraries to /extras/ directory
[*] UPDATED: Multiple bindings to latest version
[*] UPDATED: Most external libraries to latest versions (except GLFW)
[*] Multiple code improvements and fixes by multiple contributors!

-------------------------------------------------------------------------
Release:     raylib 3.7 (26 April 2021)
-------------------------------------------------------------------------
KEY CHANGES:
 - [rlgl] REDESIGNED: Greater abstraction level, some functionality moved to core module
 - [rlgl] REVIEWED: Instancing and stereo rendering
 - [core] REDESIGNED: VR simulator, fbo/shader exposed to user
 - [utils] ADDED: File access callbacks system
 - [models] ADDED: glTF animations support (#1551) by @object71
 - [audio] ADDED: Music streaming support from memory (#1606) by @nezvers
 - [*] RENAMED: enum types and enum values for consistency

Detailed changes:
[core] ADDED: LoadVrStereoConfig()
[core] ADDED: UnloadVrStereoConfig()
[core] ADDED: BeginVrStereoMode()
[core] ADDED: EndVrStereoMode()
[core] ADDED: GetCurrentMonitor() (#1485) by @object71
[core] ADDED: SetGamepadMappings() (#1506)
[core] RENAMED: struct Camera: camera.type to camera.projection
[core] RENAMED: LoadShaderCode() to LoadShaderFromMemory() (#1690)
[core] RENAMED: SetMatrixProjection() to rlSetMatrixProjection()
[core] RENAMED: SetMatrixModelview() to rlSetMatrixModelview()
[core] RENAMED: GetMatrixModelview() to rlGetMatrixModelview()
[core] RENAMED: GetMatrixProjection() to rlGetMatrixProjection()
[core] RENAMED: GetShaderDefault() to rlGetShaderDefault()
[core] RENAMED: GetTextureDefault() to rlGetTextureDefault()
[core] REMOVED: GetShapesTexture()
[core] REMOVED: GetShapesTextureRec()
[core] REMOVED: GetMouseCursor()
[core] REMOVED: SetTraceLogExit()
[core] REVIEWED: GetFileName() and GetDirectoryPath() (#1534) by @gilzoide
[core] REVIEWED: Wait() to support FreeBSD (#1618)
[core] REVIEWED: HighDPI support on macOS retina (#1510)
[core] REDESIGNED: GetFileExtension(), includes the .dot
[core] REDESIGNED: IsFileExtension(), includes the .dot
[core] REDESIGNED: Compresion API to use sdefl/sinfl libs
[rlgl] ADDED: SUPPORT_GL_DETAILS_INFO config flag
[rlgl] REMOVED: GenTexture*() functions (#721)
[rlgl] REVIEWED: rlLoadShaderDefault()
[rlgl] REDESIGNED: rlLoadExtensions(), more details exposed
[raymath] REVIEWED: QuaternionFromEuler() (#1651)
[raymath] REVIEWED: MatrixRotateZYX() (#1642)
[shapes] ADDED: DrawSplineBezierQuad() (#1468) by @epsilon-phase
[shapes] ADDED: CheckCollisionLines()
[shapes] ADDED: CheckCollisionPointLine() by @mkupiec1
[shapes] REVIEWED: CheckCollisionPointTriangle() by @mkupiec1
[shapes] REDESIGNED: SetShapesTexture()
[shapes] REDESIGNED: DrawCircleSector(), to use float params
[shapes] REDESIGNED: DrawCircleSectorLines(), to use float params
[shapes] REDESIGNED: DrawRing(), to use float params
[shapes] REDESIGNED: DrawRingLines(), to use float params
[textures] ADDED: DrawTexturePoly() and example (#1677) by @chriscamacho
[textures] ADDED: UnloadImageColors() for allocs consistency
[textures] RENAMED: GetImageData() to LoadImageColors()
[textures] REVIEWED: ImageClearBackground() and ImageDrawRectangleRec() (#1487) by @JeffM2501
[textures] REVIEWED: DrawTexturePro() and DrawRectanglePro() transformations (#1632) by @ChrisDill
[text] REDESIGNED: DrawFPS()
[models] ADDED: UploadMesh() (#1529)
[models] ADDED: UpdateMeshBuffer()
[models] ADDED: DrawMesh()
[models] ADDED: DrawMeshInstanced()
[models] ADDED: UnloadModelAnimations() (#1648) by @object71
[models] REMOVED: DrawGizmo()
[models] REMOVED: LoadMeshes()
[models] REMOVED: MeshNormalsSmooth()
[models] REVIEWED: DrawLine3D() (#1643)
[audio] REVIEWED: Multichannel sound system (#1548)
[audio] REVIEWED: jar_xm library (#1701) by @jmorel33
[utils] ADDED: SetLoadFileDataCallback()
[utils] ADDED: SetSaveFileDataCallback()
[utils] ADDED: SetLoadFileTextCallback()
[utils] ADDED: SetSaveFileTextCallback()
[examples] ADDED: text_draw_3d (#1689) by @Demizdor
[examples] ADDED: textures_poly (#1677) by @chriscamacho
[examples] ADDED: models_gltf_model (#1551) by @object71
[examples] RENAMED: shaders_rlgl_mesh_instanced to shaders_mesh_intancing
[examples] REDESIGNED: shaders_rlgl_mesh_instanced by @moliad
[examples] REDESIGNED: core_vr_simulator
[examples] REDESIGNED: models_yaw_pitch_roll
[build] ADDED: Config flag: SUPPORT_STANDARD_FILEIO
[build] ADDED: Config flag: SUPPORT_WINMM_HIGHRES_TIMER (#1641)
[build] ADDED: Config flag: SUPPORT_GL_DETAILS_INFO
[build] ADDED: Examples projects to VS2019 solution
[build] REVIEWED: Makefile to support PLATFORM_RPI (#1580)
[build] REVIEWED: Multiple typecast warnings by @JeffM2501
[build] REDESIGNED: VS2019 project build paths
[build] REDESIGNED: CMake build system by @object71
[*] RENAMED: Several functions parameters for consistency
[*] UPDATED: Multiple bindings to latest version
[*] UPDATED: All external libraries to latest versions
[*] Multiple code improvements and fixes by multiple contributors!

-------------------------------------------------------------------------
Release:     raylib 3.5 - 7th Anniversary Edition (25 December 2020)
-------------------------------------------------------------------------
KEY CHANGES:
 - [core] ADDED: PLATFORM_DRM to support RPI4 and other devices (#1388) by @kernelkinetic
 - [core] REDESIGNED: Window states management system through FLAGS
 - [rlgl] ADDED: RenderBatch type and related functions to allow custom batching (internal only)
 - [rlgl] REDESIGNED: Framebuffers API to support multiple attachment types (#721)
 - [textures] REDESIGNED: Image*() functions, big performance improvements (software rendering)
 - [*] REVIEWED: Multiple functions to replace file accesses by memory accesses
 - [*] ADDED: GitHub Actions CI to support multiple raylib build configurations

Detailed changes:
[core] ADDED: SetWindowState() / ClearWindowState() -> New flags added!
[core] ADDED: IsWindowFocused()
[core] ADDED: GetWindowScaleDPI()
[core] ADDED: GetMonitorRefreshRate() (#1289) by @Shylie
[core] ADDED: IsCursorOnScreen() (#1262) by @ChrisDill
[core] ADDED: SetMouseCursor() and GetMouseCursor() for standard Desktop cursors (#1407) by @chances
[core] REMOVED: struct RenderTexture2D: depthTexture variable
[core] REMOVED: HideWindow() / UnhideWindow() -> Use SetWindowState()
[core] REMOVED: DecorateWindow() / UndecorateWindow() -> Use SetWindowState()
[core] RENAMED: GetExtension() to GetFileExtension()
[core] REVIEWED: Several structs to reduce size and padding
[core] REVIEWED: struct Texture maps to Texture2D and TextureCubemap
[core] REVIEWED: ToggleFullscreen() (#1287)
[core] REVIEWED: InitWindow(), support empty title for window (#1323)
[core] REVIEWED: RPI: Mouse movements are bound to the screen resolution (#1392) (#1410) by @kernelkinetic
[core] REVIEWED: GetPrevDirectoryPath() fixes on Unix-like systems (#1246) by @ivan-cx
[core] REPLACED: rgif.h by msf_gif.h for automatic gif recording
[core] REDESIGNED: GetMouseWheelMove() to return float movement for precise scrolling (#1397) by @Doy-lee
[core] REDESIGNED: GetKeyPressed(), and added GetCharPressed() (#1336)
[core] UWP rework with improvements (#1231) by @Rover656
[core] Gamepad axis bug fixes and improvement (#1228) by @mmalecot
[core] Updated joystick mappings with latest version of gamecontrollerdb (#1381) by @coderoth
[rlgl] Corrected issue with OpenGL 1.1 support
[rlgl] ADDED: rlDrawMeshInstanced() (#1318) by @seanpringle
[rlgl] ADDED: rlCheckErrors (#1321) by @seanpringle
[rlgl] ADDED: BLEND_SET blending mode (#1251) by @RandomErrorMessage
[rlgl] ADDED: rlSetLineWidth(), rlGetLineWidth(), rlEnableSmoothLines(), rlDisableSmoothLines() (#1457) by @JeffM2501
[rlgl] RENAMED: rlUnproject() to Vector3Unproject() [raymath]
[rlgl] REVIEWED: Replace rlglDraw() calls by DrawRenderBatch() internal calls
[rlgl] REVIEWED: GenTextureCubemap(), use rlgl functionality only
[rlgl] REVIEWED: rlFramebufferAttach() to support texture layers
[rlgl] REVIEWED: GenDrawCube() and GenDrawQuad()
[rlgl] REVIEWED: Issues with vertex batch overflow (#1223)
[rlgl] REVIEWED: rlUpdateTexture(), issue with offsets
[rlgl] REDESIGNED: GenTexture*() to use the new fbo API (#721)
[raymath] ADDED: Normalize() and Remap() functions (#1247) by @NoorWachid
[raymath] ADDED: Vector2Reflect() (#1400) by @daniel-junior-dube
[raymath] ADDED: Vector2LengthSqr() and Vector3LengthSqr() (#1248) by @ThePituLegend
[raymath] ADDED: Vector2MoveTowards() function (#1233) by @anatagawa
[raymath] REVIEWED: Some functions consistency (#1197) by @Not-Nik
[raymath] REVIEWED: QuaternionFromVector3ToVector3() (#1263) by @jvocaturo
[raymath] REVIEWED: MatrixLookAt(), optimized (#1442) by @RandomErrorMessage
[shapes] ADDED: CheckCollisionLines(), by @Elkantor
[text] Avoid [textures] functions dependencies
[text] ADDED: Config flag: SUPPORT_TEXT_MANIPULATION
[text] ADDED: LoadFontFromMemory() (TTF only) (#1327)
[text] ADDED: UnloadFontData()
[text] RENAMED: FormatText() -> TextFormat()
[text] REVIEWED: Font struct, added charsPadding (#1432)
[text] REVIEWED: TextJoin()
[text] REVIEWED: TextReplace() (#1172)
[text] REVIEWED: LoadBMFont() to load data from memory (#1232)
[text] REVIEWED: GenImageFontAtlas(), fixed offset (#1171)
[text] REDESIGNED: LoadFontData(), reviewed input parameters
[text] REDESIGNED: LoadFontDefault(), some code simplifications
[text] REDESIGNED: LoadFontFromImage(), avoid LoadImageEx()
[text] REDESIGNED: LoadFontData(), avoid GenImageColor(), ImageFormat()
[text] REDESIGNED: LoadBMFont(), avoid ImageCopy(), ImageFormat(), ImageAlphaMask()
[textures] Move Color functions from [core] to [textures] module
[textures] ADDED: ColorAlphaBlend()
[textures] ADDED: GetPixelColor()
[textures] ADDED: SetPixelColor()
[textures] ADDED: LoadImageFromMemory() (#1327)
[textures] ADDED: LoadImageAnim() to load animated sequence of images
[textures] ADDED: DrawTextureTiled() (#1291) - @Demizdor
[textures] ADDED: UpdateTextureRec()
[textures] ADDED: UnloadImageColors(), UnloadImagePalette(), UnloadWaveSamples()
[textures] REMOVED: Config flag: SUPPORT_IMAGE_DRAWING
[textures] REMOVED: LoadImageEx()
[textures] REMOVED: LoadImagePro()
[textures] REMOVED: GetImageDataNormalized(), not exposed in the API
[textures] RENAMED: ImageExtractPalette() to GetImagePalette()
[textures] RENAMED: Fade() to ColorAlpha(), added #define for compatibility
[textures] RENAMED: GetImageData() -> LoadImageColors()
[textures] RENAMED: GetImagePalette() -> LoadImagePalette()
[textures] RENAMED: GetWaveData() -> LoadWaveSamples()
[textures] REVIEWED: GetPixelDataSize() to consider compressed data properly
[textures] REVIEWED: GetTextureData(), allow retrieving 32bit float data
[textures] REVIEWED: ImageDrawText*() params order
[textures] REVIEWED: ColorAlphaBlend(), support tint color
[textures] REVIEWED: ColorAlphaBlend(), integers-version, optimized (#1218)
[textures] REVIEWED: ImageDraw(), consider negative source offset properly (#1283)
[textures] REVIEWED: ImageDraw(), optimizations test (#1218)
[textures] REVIEWED: ImageResizeCanvas(), optimization (#1218)
[textures] REVIEWED: ExportImage(), optimized
[textures] REVIEWED: ImageAlphaPremultiply(), optimization
[textures] REVIEWED: ImageAlphaClear(), minor optimization
[textures] REVIEWED: ImageToPOT(), renamed parameter
[textures] REVIEWED: ImageCrop() (#1218)
[textures] REVIEWED: ImageToPOT() (#1218)
[textures] REVIEWED: ImageAlphaCrop() (#1218)
[textures] REVIEWED: ExportImage(), optimized (#1218)
[textures] REDESIGNED: ImageCrop(), optimized (#1218)
[textures] REDESIGNED: ImageRotateCCW(), optimized (#1218)
[textures] REDESIGNED: ImageRotateCW(), optimized (#1218)
[textures] REDESIGNED: ImageFlipHorizontal(), optimized (#1218)
[textures] REDESIGNED: ImageFlipVertical(), optimized (#1218)
[textures] REDESIGNED: ImageResizeCanvas(), optimized (#1218)
[textures] REDESIGNED: ImageDrawPixel(), optimized
[textures] REDESIGNED: ImageDrawLine(), optimized
[textures] REDESIGNED: ImageDraw(), optimized (#1218)
[textures] REDESIGNED: ImageResize(), optimized (#1218)
[textures] REDESIGNED: ImageFromImage(), optimized (#1218)
[textures] REDESIGNED: ImageDraw(), optimization (#1218)
[textures] REDESIGNED: ImageAlphaClear(), optimized (#1218)
[textures] REDESIGNED: ExportImageAsCode() to use memory buffer (#1232)
[textures] REDESIGNED: ColorFromHSV()
[models] ADDED: DrawTriangle3D() and DrawTriangleStrip3D()
[models] ADDED: UnloadModelKeepMeshes()
[models] REVIEWED: LoadModel(), avoid loading texcoords and normals from model if not existent
[models] REVIEWED: GenMeshCubicmap(), added comments and simplification
[models] REVIEWED: GenMeshCubicmap(), fixed generated normals (#1244) by @GoldenThumbs
[models] REVIEWED: GenMeshPoly(), fixed buffer overflow (#1269) by @frithrah
[models] REVIEWED: LoadOBJ(): Allow for multiple materials in obj files (#1408) by @chriscamacho and @codifies
[models] REVIEWED: LoadIQM() materials loading (#1227) by @sikor666
[models] REVIEWED: LoadGLTF() to read from memory buffer
[models] REVIEWED: UpdateMesh(), fix extra memory allocated when updating color buffer (#1271) by @4yn
[models] REVIEWED: MeshNormalsSmooth() (#1317) by @seanpringle
[models] REVIEWED: DrawGrid() (#1417)
[models] REDESIGNED: ExportMesh() to use memory buffer (#1232)
[models] REDESIGNED: LoadIQM() and LoadModelAnimations() to use memory buffers
[audio] ADDED: LoadWaveFromMemory() (#1327)
[audio] REMOVED: SetMusicLoopCount()
[audio] REVIEWED: Several functions, sampleCount vs frameCount (#1423)
[audio] REVIEWED: SaveWAV() to use memory write insted of file
[audio] REVIEWED: LoadMusicStream(), support WAV music streaming (#1198)
[audio] REVIEWED: Support multiple WAV sampleSize for MusicStream (#1340)
[audio] REVIEWED: SetAudioBufferPitch()
[audio] REDESIGNED: Audio looping system
[audio] REDESIGNED: LoadSound(): Use memory loading (WAV, OGG, MP3, FLAC) (#1312)
[audio] REDESIGNED: ExportWaveAsCode() to use memory buffers
[utils] ADDED: MemAlloc() / MemFree() (#1440)
[utils] ADDED: UnloadFileData() / UnloadFileText()
[utils] REVIEWED: android_fopen() to support SDCard access
[utils] REDESIGNED: SaveFile*() functions to expose file access results (#1420)
[rmem] REVIEWED: MemPool and other allocators optimization (#1211) by @assyrianic
[examples] ADDED: core/core_window_flags
[examples] ADDED: core/core_quat_conversion by @chriscamacho and @codifies
[examples] ADDED: textures/textures_blend_modes (#1261) by @accidentalrebel
[examples] ADDED: textures/textures_draw_tiled (#1291) by @Demizdor
[examples] ADDED: shaders/shaders_hot_reloading (#1198)
[examples] ADDED: shaders/shaders_rlgl_mesh_instanced (#1318) by @seanpringle
[examples] ADDED: shaders/shaders_multi_sampler2d
[examples] ADDED: others/embedded_files_loading
[examples] REVIEWED: textures/textures_raw_data (#1286)
[examples] REVIEWED: textures/textures_sprite_explosion, replace resources
[examples] REVIEWED: textures/textures_particles_blending, replace resources
[examples] REVIEWED: textures/textures_image_processing, support mouse
[examples] REVIEWED: models/models_skybox to work on OpenGL ES 2.0
[examples] REVIEWED: audio/resources, use open license resources
[examples] REVIEWED: others/raudio_standalone.c
[build] ADDED: New config.h configuration options exposing multiple #define values
[build] REMOVED: ANGLE VS2017 template project
[build] REVIEWED: All MSVC compile warnings
[build] Updated Makefile for web (#1332) by @rfaile313
[build] Updated build pipelines to use latest emscripten and Android NDK
[build] Updated emscriptem build script to generate .a on WebAssembly
[build] Updated Android build for Linux, supporting ANDROID_NDK at compile time by @branlix3000
[build] Updated VSCode project template tasks
[build] Updated VS2017.UWP project template by @Rover656
[build] Updated Android build pipeline
[build] REMOVED: AppVeyor and Travis CI build systems
[*] Moved raysan5/raylib/games to independent repo: raysan5/raylib-games
[*] Replaced several examples resources with more open licensed alternatives
[*] Updated BINDINGS.md with NEW bindings and added raylib version binding!
[*] Updated all external libraries to latest versions
[*] Multiple code improvements and small fixes

-----------------------------------------------
Release:     raylib 3.0 (01 April 2020)
-----------------------------------------------
KEY CHANGES:
 - Global context states used on all modules.
 - Custom memory allocators for all modules and dependencies.
 - Centralized file access system and memory data loading.
 - Structures reviewed to reduce size and always be used as pass-by-value.
 - Tracelog messages completely reviewed and categorized.
 - raudio module reviewed to accomodate new Music struct and new miniaudio.
 - text module reviewed to improve fonts generation and text management functions.
 - Multiple new examples added and categorized examples table.
 - GitHub Actions CI implemented for Windows, Linux and macOS.

Detailed changes:
[build] ADDED: VS2017.ANGLE project, by @msmshazan
[build] ADDED: VS2017 project support for x64 platform configuration
[build] ADDED: Makefile for Android building on macOS, by @Yunoinsky
[build] ADDED: Makefile for Android building on Linux, by @pamarcos
[build] REMOVED: VS2015 project
[build] REVIEWED: VSCode project
[build] REVIEWED: Makefile build system
[build] REVIEWED: Android building, by @NimbusFox
[build] REVIEWED: Compilation with CLion IDE, by @Rover656
[build] REVIEWED: Generation of web examples, by @pamarcos
[build] REVIEWED: Makefiles path to 'shell.html', by @niorad
[build] REVIEWED: VS2017 64bit compilation issues, by @spec-chum
[build] REVIEWED: Multiple fixes on projects building, by @ChrisDill, @JuDelCo, @electronstudio
[core] ADDED: Support touch/mouse indistinctly
[core] ADDED: FLAG_WINDOW_ALWAYS_RUN to avoid pause on minimize
[core] ADDED: Config flag SUPPORT_HALFBUSY_WAIT_LOOP
[core] ADDED: RPI mouse cursor point support on native mode
[core] ADDED: GetWorldToScreen2D()- Get screen space position for a 2d camera world space position, by @arvyy
[core] ADDED: GetScreenToWorld2D() - Get world space position for a 2d camera screen space position, by @arvyy
[core] ADDED: GetWorldToScreenEx() - Get size position for a 3d world space position
[core] ADDED: DirectoryExists() - Check if a directory path exists
[core] ADDED: GetPrevDirectoryPath() - Get previous directory path for a given path
[core] ADDED: CompressData() - Compress data (DEFLATE algorythm)
[core] ADDED: DecompressData() - Decompress data (DEFLATE algorythm)
[core] ADDED: GetWindowPosition() - Get window position XY on monitor
[core] ADDED: LoadFileData() - Load file data as byte array (read)
[core] ADDED: SaveFileData() - Save data to file from byte array (write)
[core] ADDED: LoadFileText() - Load text data from file (read), returns a '\0' terminated string
[core] ADDED: SaveFileText() - Save text data to file (write), string must be '\0' terminated
[core] REMOVED: Show raylib logo at initialization
[core] REVIEWED: GetFileName(), security checks
[core] REVIEWED: LoadStorageValue(), by @danimartin82
[core] REVIEWED: SaveStorageValue(), by @danimartin82
[core] REVIEWED: IsMouseButtonReleased(), when press/release events come too fast, by @oswjk
[core] REVIEWED: SetWindowMonitor(), by @DropsOfSerenity
[core] REVIEWED: IsFileExtension() to be case-insensitive
[core] REVIEWED: IsFileExtension() when checking no-extension files
[core] REVIEWED: Default font scale filter for HighDPI mode
[core] REVIEWED: Touch input scaling for PLATFORM_WEB
[core] REVIEWED: RPI input system, by @DarkElvenAngel
[core] REVIEWED: RPI input threads issues
[core] REVIEWED: OpenGL extensions loading and freeing
[core] REVIEWED: GetDirectoryPath()
[core] REVIEWED: Camera2D behavior, by @arvyy
[core] REVIEWED: OpenGL ES 2.0 extensions check
[rlgl] ADDED: Flags to allow frustrum culling near/far distance configuration at compile time
[rlgl] ADDED: Flags to sllow MAX_BATCH_BUFFERING config at compile time
[rlgl] ADDED: GetMatrixProjection(), by @chriscamacho
[rlgl] ADDED: rlUpdateMeshAt() - Update vertex or index data on GPU, at index, by @brankoku
[rlgl] REVIEWED: Vertex padding not zeroed for quads, by @kawa-yoiko
[rlgl] REVIEWED: Read texture data as RGBA from FBO on GLES 2.0
[rlgl] REVIEWED: LoadShaderCode() for const correctness, by @heretique
[rlgl] REVIEWED: rlLoadTexture()
[rlgl] REVIEWED: rlReadTexturePixels()
[rlgl] REVIEWED: rlUpdateMesh() to supports updating indices, by @brankoku
[rlgl] REVIEWED: GenTextureCubemap(), renamed parameters for consistency
[rlgl] REVIEWED: HDR pixels loading
[raymath] ADDED: MatrixRotateXYZ(), by @chriscamacho
[raymath] RENAMED: Vector3Multiply() to Vector3Scale()
[camera] REVIEWED: Free camera pitch, by @chriscamacho
[camera] REVIEWED: Camera not working properly at z-align, by @Ushio
[shapes] ADDED: DrawTriangleStrip() - Draw a triangle strip defined by points
[shapes] ADDED: DrawEllipse() - Draw ellipse
[shapes] ADDED: DrawEllipseLines() - Draw ellipse outline
[shapes] ADDED: DrawPolyLines() - Draw a polygon outline of n sides
[shapes] REVIEWED: DrawPoly() shape rendering, by @AlexHCC
[textures] ADDED: LoadAnimatedGIF() - Load animated GIF file
[textures] ADDED: GetImageAlphaBorder() - Get image alpha border rectangle
[textures] ADDED: ImageFromImage() - Create an image from another image piece
[textures] ADDED: ImageClearBackground(), by @iamsouravgupta
[textures] ADDED: ImageDrawPixel(), by @iamsouravgupta
[textures] ADDED: ImageDrawCircle(), by @iamsouravgupta
[textures] ADDED: ImageDrawLineEx(), by @iamsouravgupta
[textures] ADDED: ImageDrawPixelV(), by @RobLoach
[textures] ADDED: ImageDrawCircleV(), by @RobLoach
[textures] ADDED: ImageDrawLineV(), by @RobLoach
[textures] ADDED: ImageDrawRectangleV(), by @RobLoach
[textures] ADDED: ImageDrawRectangleRec(), by @RobLoach
[textures] REVIEWED: ImageDrawPixel(), by @RobLoach
[textures] REVIEWED: ImageDrawLine(), by @RobLoach
[textures] REVIEWED: ImageDrawCircle(), by @RobLoach
[textures] REVIEWED: ImageDrawRectangle(), by @RobLoach
[textures] REVIEWED: ImageDraw(), now it supports color tint parameter
[textures] REVIEWED: ImageResizeCanvas()
[textures] REVIEWED: ImageCrop() with security checks
[textures] REVIEWED: ImageAlphaMask()
[textures] REVIEWED: ImageDrawRectangleLines()
[textures] REVIEWED: GetImageData()
[text] ADDED: TextCopy() - Copy one string to another, returns bytes copied
[text] ADDED: GetCodepoints() - Get all codepoints in a string
[text] ADDED: CodepointToUtf8() - Encode codepoint into utf8 text
[text] ADDED: DrawTextCodepoint() - Draw one character (codepoint)
[text] RENAMED: LoadDefaultFont() -> LoadFontDefault()
[text] RENAMED: TextCountCodepoints() -> GetCodepointsCount()
[text] REVIEWED: TextFormat(), to support caching, by @brankoku
[text] REVIEWED: LoadFontData(), generate empty image for space character
[text] REVIEWED: TextSplit()
[text] REVIEWED: TextToInteger()
[text] REVIEWED: GetNextCodepoint(), renamed parameters for clarity
[text] REVIEWED: GenImageFontAtlas(), improved atlas size computing
[text] REDESIGNED: struct Font, character rectangles have been moved out from CharInfo to Font
[text] REDESIGNED: struct CharInfo, now includes directly an Image of the glyph
[text] REDESIGNED: GenImageFontAtlas(), additional recs parameter added
[text] REDESIGNED: ImageTextEx(), to avoid font retrieval from GPU
[models] ADDED: Support rlPushMatrix() and rlPopMatrix() on mesh drawing
[models] ADDED: DrawPoint3D() - Draw a point in 3D space, actually a small line, by @ProfJski
[models] ADDED: Multi texture support for materials in GLTF format, by @Gamerfiend, @chriscamacho
[models] REVIEWED: LoadGLTF(), fixed memory leak, by @jubalh
[models] REVIEWED: LoadIQM(), support multiple animations loading, by @culacant
[models] REVIEWED: GetCollisionRayModel(), to avoid pointers
[models] REVIEWED: CheckCollisionRay*(), parameters renamed
[models] REVIEWED: UnloadMesh(), to avoid pointers
[models] REVIEWED: LoadModel(), memory initialization
[models] REVIEWED: UpdateModelAnimation(), added security checks
[models] REVIEWED: Multiple fixes on models loading, by @jubalh
[models] REVIEWED: Normals updated when using animated meshes, by @@las3rlars
[models] REVIEWED: Compilation when the SUPPORT_MESH_GENERATION not set, by @@Elkantor
[raudio] ADDED: Multi-channel audio playing, by @chriscamacho
[raudio] REMOVED: LoadWaveEx()
[raudio] RENAMED: IsAudioBufferProcessed() to IsAudioStreamProcessed()
[raudio] REVIEWED: Ensure .xm playback starts in the right place, by @illegalinstruction
[raudio] REVIEWED: Fix short non-looping sounds, by @jbosh
[raudio] REVIEWED: Modules playing time to full length
[raudio] REDESIGNED: Replaced Music pointer by struct
[raudio] REDESIGNED: Removed sampleLeft from Music struct
[examples] ADDED: core_scissor_test, by @ChrisDill
[examples] ADDED: core_2d_camera_platformer, by @arvyy
[examples] ADDED: textures_mouse_painting, by @ChrisDill
[examples] ADDED: models_waving_cubes, by @codecat
[examples] ADDED: models_solar_system, by @aldrinmartoq
[examples] ADDED: shaders_fog, by @chriscamacho
[examples] ADDED: shaders_texture_waves, by @Anata
[examples] ADDED: shaders_basic_lighting, by @chriscamacho
[examples] ADDED: shaders_simple_mask, by @chriscamacho
[examples] ADDED: audio_multichannel_sound, by @chriscamacho
[examples] ADDED: shaders_spotlight, by @chriscamacho
[examples] RENAMED: text_sprite_font > text_font_spritefont
[examples] RENAMED: text_ttf_loading > text_font_filters
[examples] RENAMED: text_bmfont_ttf > text_font_loading
[examples] REMOVED: models_obj_viewer
[examples] REMOVED: models_solar_system
[examples] REVIEWED: models_obj_loading > models_loading
[examples] REVIEWED: models_materials_pbr, shader issues
[examples] REVIEWED: core_window_letterbox, detailed explanation, by @jotac0
[examples] REVIEWED: core_window_letterbox, virtual mouse, by @anatagawa
[games] ADDED: GGJ2020 game - RE-PAIR
[*] Misc fixes and tweaks, by @yaram, @oraoto, @zatherz, @piecedigital, @Shylie
[*] Update ALL supported projects (Notepad++, VS2017)
[*] Update ALL external libraries to latest versions (29.Jan.2020)
[*] Update ALL examples and games
[*] Update BINDINGS list

-----------------------------------------------
Release:     raylib 2.5 (May 2019)
-----------------------------------------------
KEY CHANGES:
 - [core] Redesigned Gamepad mechanism, now common to all platforms and gamepads
 - [core] HighDPI monitors support with automatic content scaling
 - [rlgl] Complete module redesign to use one single internal buffer
 - [rlgl] VR system redesign to allow custom device parameters and distortion shader
 - [shapes] New drawing shapes available: CircleSector, Ring and RectangleRounded
 - [text] New text management API (multiple functions)
 - [text] Full Unicode support (utf8 text)
 - [textures] Cubemap textures support
 - [textures] Quad and N-Patch drawing
 - [models] Skeletal model animation support
 - [models] Support multiple meshes per model
 - [models] Support glTF model loading

Detailed changes:
[build] REVIEWED: Default raylib and examples Makefile
[build] REVIEWED: Notepad++ NppExec scripts
[build] REVIEWED: VS2015 and VS2017 projects
[build] REVIEWED: Android APK build pipeline
[core] Converted most #defined values as enum values
[core] Complete redesign of RPI input system to use evdev events
[core] ADDED: IsWindowResized() - Check if window has been resized
[core] ADDED: IsWindowHidden() - Check if window is currently hidden
[core] ADDED: UnhideWindow() - Show the window
[core] ADDED: HideWindow() - Hide the window
[core] ADDED: GetWindowHandle() - Get native window handle
[core] ADDED: GetMonitorCount() - Get number of connected monitors
[core] ADDED: GetMonitorWidth() - Get primary monitor width
[core] ADDED: GetMonitorHeight() - Get primary monitor height
[core] ADDED: GetMonitorPhysicalWidth() - Get primary monitor physical width in millimetres
[core] ADDED: GetMonitorPhysicalHeight() - Get primary monitor physical height in millimetres
[core] ADDED: GetMonitorName() - Get the human-readable, UTF-8 encoded name of the primary monitor
[core] ADDED: GetClipboardText() - Get clipboard text content
[core] ADDED: SetClipboardText() - Set clipboard text content
[core] ADDED: ColorFromHSV() - Returns a Color from HSV values
[core] ADDED: FileExists() - Check if file exists
[core] ADDED: GetFileNameWithoutExt() - Get filename string without extension (memory should be freed)
[core] ADDED: GetDirectoryFiles() - Get filenames in a directory path (memory should be freed)
[core] ADDED: ClearDirectoryFiles() - Clear directory files paths buffers (free memory)
[core] ADDED: OpenURL() - Open URL with default system browser (if available)
[core] ADDED: SetMouseOffset() - Set mouse offset
[core] ADDED: SetMouseScale() - Set mouse scaling
[core] REMOVED: ShowLogo() - Activate raylib logo at startup (can be done with flags)
[shapes] ADDED: DrawCircleSector() - Draw a piece of a circle
[shapes] ADDED: DrawCircleSectorLines() - Draw circle sector outline
[shapes] ADDED: DrawRing() - Draw ring
[shapes] ADDED: DrawRingLines() - Draw ring outline
[shapes] ADDED: DrawRectangleRounded() - Draw rectangle with rounded edges
[shapes] ADDED: DrawRectangleRoundedLines() - Draw rectangle with rounded edges outline
[shapes] ADDED: SetShapesTexture() - Define default texture used to draw shapes
[textures] REVIEWED: ExportImage() - Reorder function parameters
[textures] REVIEWED: ImageDrawRectangle() - Remove unneeded parameter
[textures] ADDED: ExportImageAsCode() - Export image as code file defining an array of bytes
[textures] ADDED: LoadTextureCubemap() - Load cubemap from image, multiple image cubemap layouts supported
[textures] ADDED: ImageExtractPalette() - Extract color palette from image to maximum size (memory should be freed)
[textures] ADDED: ImageDrawRectangleLines() - Draw rectangle lines within an image
[textures] ADDED: DrawTextureQuad() - Draw texture quad with tiling and offset parameters
[textures] ADDED: DrawTextureNPatch() - Draws a texture (or part of it) that stretches or shrinks nicely
[models] REVIEWED: LoadMesh() -> LoadMeshes() - Support multiple meshes loading
[models] REVIEWED: LoadMaterial() -> LoadMaterials() - Support multiple materials loading
[models] REVIEWED: ExportMesh() - Reorder parameters
[models] ADDED: DrawCubeWiresV() - Draw cube wires (Vector version)
[models] ADDED: GenMeshPoly() - Generate polygonal mesh
[models] ADDED: SetMaterialTexture() - Set texture for a material map type (MAP_DIFFUSE, MAP_SPECULAR...)
[models] ADDED: SetModelMeshMaterial() - Set material for a mesh
[models] ADDED: LoadModelAnimations() - Load model animations from file
[models] ADDED: UpdateModelAnimation() - Update model animation pose
[models] ADDED: UnloadModelAnimation() - Unload animation data
[models] ADDED: IsModelAnimationValid() - Check model animation skeleton match
[rlgl] Improved internal batching mechanism (multibuffering support, triangle texcoords...)
[rlgl] REVIEWED: rlPushMatrix()/rlPopMatrix() - Now works like OpenGL 1.1
[rlgl] REVIEWED: SetShaderValue() - More generic, now requires uniform type
[rlgl] REMOVED: SetShaderValuei() - Can be acoomplished with new SetShaderValue()
[rlgl] ADDED: SetShaderValueV() - Set shader uniform value vector
[rlgl] ADDED: SetShaderValueTexture() - Set shader uniform value for texture
[rlgl] ADDED: BeginScissorMode() - Begin scissor mode (define screen area for following drawing)
[rlgl] ADDED: EndScissorMode() - End scissor mode
[rlgl] ADDED: SetVrConfiguration() - Set stereo rendering configuration parameters
[rlgl] REVIEWED: InitVrSimulator() - No input parameter required, use SetVrConfiguration()
[text] REVIEWED: LoadFontEx() - Reorder function parameters
[text] REVIEWED: LoadFontData() - Reorder function parameters
[text] REVIEWED: GenImageFontAtlas() - Reorder function parameters
[text] RENAMED: FormatText() -> TextFormat()
[text] RENAMED: SubText() -> TextSubtext()
[text] ADDED: LoadFontFromImage() - Load font from Image (XNA style)
[text] ADDED: DrawTextRec() - Draw text using font inside rectangle limits
[text] ADDED: DrawTextRecEx() - Draw text using font inside rectangle limits with support for text selection
[text] ADDED: TextIsEqual() - Check if two text string are equal
[text] ADDED: TextLength() - Get text length, checks for '\0' ending
[text] ADDED: TextReplace() - Replace text string (memory should be freed!)
[text] ADDED: TextInsert() - Insert text in a position (memory should be freed!)
[text] ADDED: TextJoin() - Join text strings with delimiter
[text] ADDED: TextSplit() - Split text into multiple strings
[text] ADDED: TextAppend() - Append text at specific position and move cursor!
[text] ADDED: TextFindIndex() - Find first text occurrence within a string
[text] ADDED: TextToUpper() - Get upper case version of provided string
[text] ADDED: TextToLower() - Get lower case version of provided string
[text] ADDED: TextToPascal() - Get Pascal case notation version of provided string
[text] ADDED: TextToInteger() - Get integer value from text (negative values not supported)
[raudio] ADDED: ExportWave() - Export wave data to file
[raudio] ADDED: ExportWaveAsCode() - Export wave sample data to code (.h)
[raudio] ADDED: IsAudioStreamPlaying() - Check if audio stream is playing
[raudio] ADDED: SetAudioStreamVolume() - Set volume for audio stream (1.0 is max level)
[raudio] ADDED: SetAudioStreamPitch() - Set pitch for audio stream (1.0 is base level)
[examples] Complete review of full examples collection, many additions
[examples] ADDED: core_custom_logging - Custom trace log system
[examples] ADDED: core_input_multitouch - Multitouch input example
[examples] ADDED: core_window_letterbox - Window adapted to screen
[examples] ADDED: core_loading_thread - Data loading in second thread
[examples] REVIEWED: core_input_gamepad - Adapted to new gamepad system
[examples] REVIEWED: core_vr_simulator - HMD device parameters and distortion shader should be provided
[examples] ADDED: core_window_scale_letterbox - Windows resizing and letterbox content
[examples] ADDED: shapes_rectangle_scaling_mouse - Scale a rectangle with mouse
[examples] ADDED: shapes_draw_circle_sector - Circle sector drawing
[examples] ADDED: shapes_draw_ring - Ring drawing
[examples] ADDED: shapes_draw_rectangle_rounded - Rounded rectangle drawing
[examples] ADDED: shapes_bouncing_ball - Ball bouncing in the screen
[examples] ADDED: shapes_collision_area - Collision detection and drawing
[examples] ADDED: shapes_following_eyes - Some maths on eyes and mouse
[examples] ADDED: shapes_easings_ball_anim - Ball animation
[examples] ADDED: shapes_easings_box_anim - Box animation
[examples] ADDED: shapes_easings_rectangle_array - Rectangles animation
[examples] REVIEWED: shapes_colors_palette - Reviewed color selection and text displaying
[examples] ADDED: textures_background_scrolling - Scrolling and parallaz background effect
[examples] ADDED: textures_image_npatch - Drawing N-Patch based boxes
[examples] ADDED: textures_sprite_button - Sprite button with sound
[examples] ADDED: textures_sprite_explosion - Sprite explosion with sound
[examples] ADDED: textures_bunnymark - Benchmarking test
[examples] ADDED: text_draw_inside_rectangle - Drawing text inside a delimited rectangle box
[examples] ADDED: text_unicode - Multiple languages text drawing
[examples] ADDED: text_rectangle_bound - Fit text inside a rectangle
[examples] REVIEWED: text_bmfont_ttf - Simplified example
[examples] ADDED: models_animation - Animated models loading and animation playing
[examples] ADDED: models_obj_viewer - Draw and drop models viewer
[examples] ADDED: models_rlgl_solar_system - Solar system simulation using rlgl functionality
[examples] ADDED: models_first_person_maze - 3D maze fps
[examples] ADDED: shaders_palette_switch - Switching color palette on shader
[examples] ADDED: shaders_raymarching - Raymarching shader
[examples] ADDED: shaders_texture_drawing - Texture drawing on GPU
[examples] ADDED: shaders_texture_waves - Texture waves on shader
[examples] ADDED: shaders_julia_set - Julia set fractals
[examples] ADDED: shaders_eratosthenes - Prime number visualization shader
[examples] REVIEWED: audio_raw_stream - Mostly rewritten
[games] ADDED: GGJ19 game - Cat vs Roomba
[*] Updated external libraries to latest version
[*] Multiple bugs corrected (check github issues)

-----------------------------------------------
Release:     raylib 2.0 (July 2018)
-----------------------------------------------
KEY CHANGES:
  - Removed external dependencies (GLFW3 and OpenAL)
  - Complete redesign of audio module to use miniaudio library
  - Support AppVeyor and Travis CI (continuous integration) building
  - Reviewed raymath.h for better consistency and performance (inlining)
  - Refactor all #define SUPPORT_* into a single config.h
  - Support TCC compiler (32bit and 64bit)

Detailed changes:
[build] REMOVED: GitHub develop branch
[build] REMOVED: External dependencies GLFW and OpenAL
[build] ADDED: Android 64bit ARM support
[build] ADDED: FreeBSD, OpenBSD, NetBSD, Dragon Fly OS support
[build] ADDED: Universal Windows Platform (UWP) support
[build] ADDED: Wayland Linux desktop support
[build] ADDED: AppVeyor CI for automatic Windows builds
[build] ADDED: Travis CI for automatic Linux/macOS builds
[build] ADDED: rglfw (GLFW3 module) to avoid external dependency
[build] ADDED: VS2017 UWP project
[build] ADDED: Builder project template
[build] ADDED: Compiler memory sanitizer for better debug
[build] ADDED: CMake package target and CI auto-deploy tags
[build] ADDED: DEBUG library building support
[build] ADDED: Notepad++ NppExec scripts
[build] REVIEWED: VS2015 and VS2017 projects
[build] REVIEWED: Android APK build pipeline
[core] REVIEWED: Window creation hints to support transparent windows
[core] Unified InitWindow() between platforms
[core] Export Android main entry point
[core] RENAMED: Begin3dMode() to BeginMode3D()
[core] RENAMED: End3dMode() to EndMode3D()
[core] RENAMED: Begin2dMode() to BeginMode2D()
[core] RENAMED: End2dMode() to EndMode2D()
[core] RENAMED: struct Camera to Camera3D
[core] RENAMED: struct SpriteFont to Font -> plus all required functions!
[core] RENAMED: enum TextureFormat to PixelFormat
[core] REVIEWED: Rectangle params int to float
[core] REVIEWED: timing system for macOS
[core] REMOVED: ColorToFloat()
[core] ADDED: GetCurrentTime() on macOS
[core] ADDED: GetTime()
[core] ADDED: struct Vector4
[core] ADDED: SetTraceLog() to define trace log messages type
[core] ADDED: GetFileName() to get filename from path string
[core] ADDED: ColorToHSV()
[core] ADDED: ColorNormalize()
[core] ADDED: SetWindowSize() to scale Windows in runtime
[core] ADDED: SetMouseScale() to scale mouse input
[core] ADDED: key definitions - KEY_GRAVE, KEY_SLASH, KEY_BACKSLASH
[core] RENAMED: GetHexValue() to ColorToInt()
[core] REVIEWED: Fade()
[core] REVIEWED: InitWindow() to avoid void pointer (safety)
[core] Support camera 3d orthographic projection mode
[shapes] ADDED: DrawRectangleLinesEx()
[textures] Improved pixel formats support (32bit channels)
[textures] Improved textures support for OpenGL 2.1
[textures] REMOVED: DrawRectangleT() --> Added support to DrawRectangle()
[textures] ADDED: GetPixelDataSize(); pixel data size in bytes (image or texture)
[textures] ADDED: ImageAlphaClear() --> Clear alpha channel to desired color
[textures] ADDED: ImageAlphaCrop() --> Crop image depending on alpha value
[textures] ADDED: ImageAlphaPremultiply() --> Premultiply alpha channel
[textures] ADDED: ImageDrawRectangle()
[textures] ADDED: ImageMipmaps()
[textures] ADDED: GenImageColor()
[textures] ADDED: GetPixelDataSize()
[textures] ADDED: ImageRotateCW()
[textures] ADDED: ImageRotateCCW()
[textures] ADDED: ImageResizeCanvas()
[textures] ADDED: GetImageDataNormalized()
[textures] REVIEWED: ImageFormat() to use normalized data
[textures] REVIEWED: Manual mipmap generation
[textures] REVIEWED: LoadASTC()
[textures] REVIEWED: GenImagePerlinNoise()
[textures] REVIEWED: ImageTextEx() to support UTF8 basic characters
[textures] REVIEWED: GetTextureData() for RPI - requires some work
[textures] Added new example: text drawing on image
[text] Corrected issue with ttf font y-offset
[text] Support SDF font data generation
[text] ADDED: GenImageFontAtlas()
[text] ADDED: LoadFontData() to load data from TTF file
[text] REMOVED: LoadTTF() internal function
[text] REVIEWED: DrawTextEx() - avoid rendering SPACE character!
[text] RENAMED: GetDefaultFont() to GetFontDefault()
[rlgl] ADDED: rlCheckBufferLimit()
[rlgl] ADDED: LoadShaderCode()
[rlgl] ADDED: GetMatrixModelview()
[rlgl] ADDED: SetVrDistortionShader(Shader shader)
[rlgl] REVIEWED: rlLoadTexture() - added mipmaps support, improved compressed textures loading
[rlgl] REVIEWED: rlReadTexturePixels()
[models] Support 4 components mesh.tangent data
[models] Removed tangents generation from LoadOBJ()
[models] ADDED: MeshTangents()
[models] ADDED: MeshBinormals()
[models] ADDED: ExportMesh()
[models] ADDED: GetCollisionRayModel()
[models] RENAMED: CalculateBoundingBox() to MeshBoundingBox()
[models] REMOVED: GetCollisionRayMesh() - does not consider model transform
[models] REVIEWED: LoadMesh() - fallback to default cube mesh if loading fails
[audio] ADDED: Support for MP3 fileformat
[audio] ADDED: IsAudioStreamPlaying()
[audio] ADDED: SetAudioStreamVolume()
[audio] ADDED: SetAudioStreamPitch()
[utils] Corrected issue with SaveImageAs()
[utils] RENAMED: SaveImageAs() to ExportImage()
[utils] REMOVED: rres support - moved to external library (rres.h)
[shaders] REVIEWED: GLSL 120 shaders
[raymath] ADDED: Vector3RotateByQuaternion()
[raymath] REVIEWED: math usage to reduce temp variables
[raymath] REVIEWED: Avoid pointer-based parameters for API consistency
[physac] REVIEWED: physac.h timing system
[examples] Replaced dwarf model by brand new 3d assets: 3d medieval buildings
[examples] Assets cleaning and some replacements
[games] ADDED: GGJ18 game - transmission mission
[games] REVIEWED: Light my Ritual game - improved gameplay drawing
[*] Updated external libraries to latest version
[*] Multiple bugs corrected (check github issues)

-----------------------------------------------
Release:     raylib 1.8.0 (Oct 2017)
-----------------------------------------------
NOTE:
  In this release, multiple parts of the library have been reviewed (again) for consistency and simplification.
  It exposes more than 30 new functions in comparison with previous version and it improves overall programming experience.

BIG CHANGES:
  - New Image generation functions: Gradient, Checked, Noise, Cellular...
  - New Mesh generation functions: Cube, Sphere, Cylinder, Torus, Knot...
  - New Shaders and Materials systems to support PBR materials
  - Custom Android APK build pipeline with simple Makefile
  - Complete review of rlgl layer functionality
  - Complete review of raymath functionality

detailed changes:
[rlgl] RENAMED: rlglLoadTexture() to rlLoadTexture()
[rlgl] RENAMED: rlglLoadRenderTexture() to rlLoadRenderTexture()
[rlgl] RENAMED: rlglUpdateTexture() to rlUpdateTexture()
[rlgl] RENAMED: rlglGenerateMipmaps() to rlGenerateMipmaps()
[rlgl] RENAMED: rlglReadScreenPixels() to rlReadScreenPixels()
[rlgl] RENAMED: rlglReadTexturePixels() to rlReadTexturePixels()
[rlgl] RENAMED: rlglLoadMesh() to rlLoadMesh()
[rlgl] RENAMED: rlglUpdateMesh() to rlUpdateMesh()
[rlgl] RENAMED: rlglDrawMesh() to rlDrawMesh()
[rlgl] RENAMED: rlglUnloadMesh() to rlUnloadMesh()
[rlgl] RENAMED: rlglUnproject() to rlUnproject()
[rlgl] RENAMED: LoadCompressedTexture() to LoadTextureCompressed()
[rlgl] RENAMED: GetDefaultTexture() to GetTextureDefault()
[rlgl] RENAMED: LoadDefaultShader() to LoadShaderDefault()
[rlgl] RENAMED: LoadDefaultShaderLocations() to SetShaderDefaultLocations()
[rlgl] RENAMED: UnloadDefaultShader() to UnLoadShaderDefault()
[rlgl] ADDED: rlGenMapCubemap(), Generate cubemap texture map from HDR texture
[rlgl] ADDED: rlGenMapIrradiance(), Generate irradiance texture map
[rlgl] ADDED: rlGenMapPrefilter(), Generate prefilter texture map
[rlgl] ADDED: rlGenMapBRDF(), Generate BRDF texture map
[rlgl] ADDED: GetVrDeviceInfo(), Get VR device information for some standard devices
[rlgl] REVIEWED: InitVrSimulator(), to accept device parameters as input
[core] ADDED: SetWindowTitle(), Set title for window (only PLATFORM_DESKTOP)
[core] ADDED: GetExtension(), Get file extension
[shapes] REMOVED: DrawRectangleGradient(), replaced by DrawRectangleGradientV() and DrawRectangleGradientH()
[shapes] ADDED: DrawRectangleGradientV(), Draw a vertical-gradient-filled rectangle
[shapes] ADDED: DrawRectangleGradientH(), Draw a horizontal-gradient-filled rectangle
[shapes] ADDED: DrawRectangleGradientEx(), Draw a gradient-filled rectangle with custom vertex colors
[shapes] ADDED: DrawRectangleT(), Draw rectangle using text character
[textures] ADDED: SaveImageAs(), Save image as PNG file
[textures] ADDED: GenImageGradientV(), Generate image: vertical gradient
[textures] ADDED: GenImageGradientH(), Generate image: horizontal gradient
[textures] ADDED: GenImageGradientRadial(), Generate image: radial gradient
[textures] ADDED: GenImageChecked(), Generate image: checked
[textures] ADDED: GenImageWhiteNoise(), Generate image: white noise
[textures] ADDED: GenImagePerlinNoise(), Generate image: perlin noise
[textures] ADDED: GenImageCellular(), Generate image: cellular algorithm. Bigger tileSize means bigger cells
[textures] ADDED: GenTextureCubemap(), Generate cubemap texture from HDR texture
[textures] ADDED: GenTextureIrradiance(), Generate irradiance texture using cubemap data
[textures] ADDED: GenTexturePrefilter(), Generate prefilter texture using cubemap data
[textures] ADDED: GenTextureBRDF(), Generate BRDF texture using cubemap data
[models] REMOVED: LoadMeshEx(), Mesh struct variables can be directly accessed
[models] REMOVED: UpdateMesh(), very ineficient
[models] REMOVED: LoadHeightmap(), use GenMeshHeightmap() and LoadModelFromMesh()
[models] REMOVED: LoadCubicmap(), use GenMeshCubicmap() and LoadModelFromMesh()
[models] RENAMED: LoadDefaultMaterial() to LoadMaterialDefault()
[models] ADDED: GenMeshPlane(), Generate plane mesh (with subdivisions)
[models] ADDED: GenMeshCube(), Generate cuboid mesh
[models] ADDED: GenMeshSphere(), Generate sphere mesh (standard sphere)
[models] ADDED: GenMeshHemiSphere(), Generate half-sphere mesh (no bottom cap)
[models] ADDED: GenMeshCylinder(), Generate cylinder mesh
[models] ADDED: GenMeshTorus(), Generate torus mesh
[models] ADDED: GenMeshKnot(), Generate trefoil knot mesh
[models] ADDED: GenMeshHeightmap(), Generate heightmap mesh from image data
[models] ADDED: GenMeshCubicmap(), Generate cubes-based map mesh from image data
[raymath] REVIEWED: full Matrix functionality to align with GLM in usage
[raymath] RENAMED: Vector3 functions for consistency: Vector*() renamed to Vector3*()
[build] Integrate Android APK building into examples Makefile
[build] Integrate Android APK building into templates Makefiles
[build] Improved Visual Studio 2015 project, folders, references...
[templates] Reviewed the full pack to support Android building
[examples] Reviewed full collection to adapt to raylib changes
[examples] [textures] ADDED: textures_image_generation
[examples] [models] ADDED: models_mesh_generation
[examples] [models] ADDED: models_material_pbr
[examples] [models] ADDED: models_skybox
[examples] [models] ADDED: models_yaw_pitch_roll
[examples] [others] REVIEWED: rlgl_standalone
[examples] [others] REVIEWED: audio_standalone
[github] Moved raylib webpage to own repo: github.com/raysan5/raylib.com
[games] Reviewed game: Koala Seasons
[*] Updated STB libraries to latest version
[*] Multiple bugs corrected (check github issues)

-----------------------------------------------
Release:     raylib 1.7.0 (20 May 2017)
-----------------------------------------------
NOTE:
  In this new raylib release, multiple parts of the library have been reviewed for consistency and simplification.
  It exposes almost 300 functions, around 30 new functions in comparison with previous version and, again,
  it sets a stepping stone towards raylib future.

BIG changes:
  - More than 30 new functions added to the library, check list below.
  - Support of configuration flags on every raylib module, to customize library build.
  - Improved build system for all supported platforms with a unique Makefile to compile sources.
  - Complete review of examples and sample games, added new sample material.
  - Support automatic GIF recording of current window, just pressing Ctrl+F12
  - Improved library consistency and organization in general.

other changes:
[core] Added function: SetWindowIcon(), to setup icon by code
[core] Added function: SetWindowMonitor(), to set current display monitor
[core] Added function: SetWindowMinSize(), to set minimum resize size
[core] Added function: TakeScreenshot(), made public to API (also launched internally with F12)
[core] Added function: GetDirectoryPath(), get directory for a given fileName (with path)
[core] Added function: GetWorkingDirectory(), get current working directory
[core] Added function: ChangeDirectory(), change working directory
[core] Added function: TraceLog(), made public to API
[core] Improved timing system to avoid busy wait loop on frame sync: Wait()
[core] Added support for gamepad on HTML5 platform
[core] Support mouse lock, useful for camera system
[core] Review functions description comments
[rlgl] Removed function: GetStandardShader(), removed internal standard shader
[rlgl] Removed function: CreateLight(), removed internal lighting system
[rlgl] Removed function: DestroyLight(), removed internal lighting system
[rlgl] Removed function: InitVrDevice(), removed VR device render, using simulator
[rlgl] Removed function: CloseVrDevice(), removed VR device render, using simulator
[rlgl] Removed function: IsVrDeviceReady(), removed VR device render, using simulator
[rlgl] Removed function: IsVrSimulator(), removed VR device render, using simulator
[rlgl] Added function: InitVrSimulator(), init VR simulator for selected device
[rlgl] Added function: CloseVrSimulator(), close VR simulator for current device
[rlgl] Added function: IsVrSimulatorReady(), detect if VR device is ready
[rlgl] Added function: BeginVrDrawing(), begin VR simulator stereo rendering
[rlgl] Added function: EndVrDrawing(), end VR simulator stereo rendering
[rlgl] Renamed function: ReadTextFile() to LoadText() and exposed to API
[rlgl] Removed internal lighting system and standard shader, moved to example
[rlgl] Removed Oculus Rift support, moved to oculus_rift example
[rlgl] Removed VR device support and replaced by VR simulator
[shapes] Added function: DrawLineEx(), draw line with QUADS, supports custom line thick
[shapes] Added function: DrawLineBezier(), draw a line using cubic-bezier curves in-out
[shapes] Added function: DrawRectanglePro(), draw a color-filled rectangle with pro parameters
[textures] Removed function: LoadImageFromRES(), redesigning custom RRES fileformat
[textures] Removed function: LoadTextureFromRES(), redesigning custom RRES fileformat
[textures] Removed function: LoadTextureEx(), use instead Image -> LoadImagePro(), LoadImageEx()
[textures] Added function: LoadImagePro()), load image from raw data with parameters
[textures] Review TraceLog() message when image file not found
[text] Renamed function: LoadSpriteFontTTF() to LoadSpriteFontEx(), for consistency
[text] Removed rBMF fileformat support, replaced by .png
[text] Refactor SpriteFont struct (better for rres custom fileformat)
[text] Renamed some variables for consistency
[models] Added function: LoadMesh(), load mesh from file
[models] Added function: LoadMeshEx(), load mesh from vertex data
[models] Added function: UnloadMesh(), unload mesh from memory (RAM and/or VRAM)
[models] Added function: GetCollisionRayMesh(), get collision info between ray and mesh
[models] Added function: GetCollisionRayTriangle(), get collision info between ray and triangle
[models] Added function: GetCollisionRayGround(), get collision info between ray and ground plane
[models] Renamed function: LoadModelEx() to LoadModelFromMesh()
[models] Removed function: DrawLight(), removed internal lighting system
[models] Renamed function: LoadModelEx() to LoadModelFromMesh() for consistency
[models] Removed function: LoadStandardMaterial(), removed internal standard shader
[models] Removed function: LoadModelFromRES(), redesigning custom RRES fileformat
[models] Renamed multiple variables for consistency
[audio] Added function: SetMasterVolume(), define listener volume
[audio] Added function: ResumeSound(), resume a paused sound
[audio] Added function: SetMusicLoopCount(), set number of repeats for a music
[audio] Added function: LoadWaveEx(), load wave from raw audio data
[audio] Added function: WaveCrop(), crop wave audio data
[audio] Added function: WaveFormat(), format audio data
[audio] Removed function: LoadSoundFromRES(), redesigning custom RRES fileformat
[audio] Added support for 32bit audio samples
[audio] Preliminary support for multichannel, limited to mono and stereo
[audio] Make sure buffers are ready for update: UpdateMusicStream()
[utils] Replaced function: GetExtension() by IsFileExtension() and made public to API
[utils] Unified function: TraceLog() between Android and other platforms
[utils] Removed internal function: GetNextPOT(), simplified implementation
[raymath] Added function: QuaternionToEuler(), to work with Euler angles
[raymath] Added function: QuaternionFromEuler(), to work with Euler angles
[raymath] Added multiple Vector2 math functions
[build] Integrate Android source building into Makefile
[example] Added example: shapes_lines_bezier
[example] Added example: text_input_box
[github] Moved gh-pages branch to master/docs
[github] Moved rlua.h and Lua examples to own repo: raylib-lua
[games] Reviewed full games collection
[games] New game added to collection: Koala Seasons
[*] Reviewed and improved examples collection (new assets)
[*] Reorganized library functions, structs, enums
[*] Updated STB libraries to latest version

-----------------------------------------------
Release:     raylib 1.6.0 (20 November 2016)
-----------------------------------------------
NOTE:
  This new raylib version commemorates raylib 3rd anniversary and represents another complete review of the library.
  It includes some interesting new features and is a stepping stone towards raylib future.

HUGE changes:
[rlua] Lua BINDING: Complete raylib Lua binding, ALL raylib functions ported to Lua plus the +60 code examples.
[audio] COMPLETE REDESIGN: Improved music support and also raw audio data processing and playing, +20 new functions added.
[physac] COMPLETE REWRITE: Improved performance, functionality and simplified usage, moved to own repository and added multiple examples!

other changes:

[core] Corrected issue on OSX with HighDPI display
[core] Added flag to allow resizable window
[core] Allow no default font loading
[core] Corrected old issue with mouse buttons on web
[core] Improved gamepad support, unified across platforms
[core] Gamepad id functionality: GetGamepadName(), IsGamepadName()
[core] Gamepad buttons/axis checking functionality:
[core] Reviewed Android key inputs system, unified with desktop
[rlgl] Redesigned lighting shader system
[rlgl] Updated standard shader for better performance
[rlgl] Support alpha on framebuffer: rlglLoadRenderTexture()
[rlgl] Reviewed UpdateVrTracking() to update camera
[rlgl] Added IsVrSimulator() to check for VR simulator
[shapes] Corrected issue on DrawPolyEx()
[textures] Simplified supported image formats support
[textures] Improved text drawing within an image: ImageDrawText()
[textures] Support image alpha mixing: ImageAlphaMask()
[textures] Support textures filtering: SetTextureFilter()
[textures] Support textures wrap modes: SetTextureWrap()
[text] Improved TTF spritefont generation: LoadSpriteFontTTF()
[text] Improved AngelCode fonts support (unordered chars)
[text] Added TraceLog info on image spritefont loading
[text] Improved text measurement: MeasureTextEx()
[models] Improved OBJ loading flexibility
[models] Reviewed functions: DrawLine3D(), DrawCircle3D()
[models] Removed function: ResolveCollisionCubicmap()
[camera] Redesigned camera system and ported to header-only
[camera] Removed function: UpdateCameraPlayer()
[gestures] Redesigned gestures module to header-only
[audio] Simplified Music loading and playing system
[audio] Added trace on audio device closing
[audio] Reviewed Wave struct, improved flexibility
[audio] Support sound data update: UpdateSound()
[audio] Added support for FLAC audio loading/streaming
[raygui] Removed raygui from raylib repo (moved to own repo)
[build] Added OpenAL static library
[build] Added Visual Studio 2015 projects
[build] Support shared/dynamic raylib compilation
[*] Updated LibOVR to SDK version 1.8
[*] Updated games to latest raylib version
[*] Improved examples and added new ones
[*] Improved Android support

-----------------------------------------------
Release:     raylib 1.5.0 (18 July 2016)
-----------------------------------------------
NOTE:
  Probably this new version is the biggest boost of the library ever, lots of parts of the library have been redesigned,
  lots of bugs have been solved and some **AMAZING** new features have been added.

HUGE changes:
[rlgl] OCULUS RIFT CV1: Added support for VR, not oly Oculus Rift CV1 but also stereo rendering simulator (multiplatform).
[rlgl] MATERIALS SYSTEM: Added support for Materials (.mtl) and multiple material properties: diffuse, specular, normal.
[rlgl] LIGHTING SYSTEM: Added support for up to 8 lights of 3 different types: Omni, Directional and Spot.
[physac] REDESIGNED: Improved performance and simplified usage, physic objects now are managed internally in a second thread!
[audio] CHIPTUNES: Added support for module audio music (.xm, .mod) loading and playing. Multiple mixing channels supported.

other changes:

[core] Review Android button inputs
[core] Support Android internal data storage
[core] Renamed WorldToScreen() to GetWorldToScreen()
[core] Removed function SetCustomCursor()
[core] Removed functions BeginDrawingEx(), BeginDrawingPro()
[core] Replaced functions InitDisplay() + InitGraphics() with: InitGraphicsDevice()
[core] Added support for field-of-view Y (fovy) on 3d Camera
[core] Added 2D camera mode functions: Begin2dMode() - End2dMode()
[core] Translate mouse inputs to Android touch/gestures internally
[core] Translate mouse inputs as touch inputs in HTML5
[core] Improved function GetKeyPressed() to support multiple keys (including function keys)
[core] Improved gamepad support, specially for RaspberryPi (including multiple gamepads support)
[rlgl] Support stereo rendering simulation (duplicate draw calls by viewport, optimized)
[rlgl] Added distortion shader (embeded) to support custom VR simulator: shader_distortion.h
[rlgl] Added support for OpenGL 2.1 on desktop
[rlgl] Improved 2D vs 3D drawing system (lines, triangles, quads)
[rlgl] Improved DXT-ETC1 support on HTML5
[rlgl] Review function: rlglUnproject()
[rlgl] Removed function: rlglInitGraphics(), integrated into rlglInit()
[rlgl] Updated Mesh and Shader structs
[rlgl] Simplified internal (default) dynamic buffers
[rlgl] Added support for indexed and dynamic mesh data
[rlgl] Set fixed vertex attribs location points
[rlgl] Improved mesh data loading support
[rlgl] Added standard shader (embeded) to support materials and lighting: shader_standard.h
[rlgl] Added light functions: CreateLight(), DestroyLight()
[rlgl] Added wire mode functions: rlDisableWireMode(), rlEnableWireMode()
[rlgl] Review function consistency, added: rlglLoadMesh(), rlglUpdateMesh(), rlglDrawMesh(), rlglUnloadMesh()
[rlgl] Replaced SetCustomShader() by: BeginShaderMode() - EndShaderMode()
[rlgl] Replaced SetBlendMode() by: BeginBlendMode() - EndBlendMode()
[rlgl] Added functions to customize internal matrices: SetMatrixProjection(), SetMatrixModelview()
[rlgl] Unified internal shaders to only one default shader
[rlgl] Added support for render to texture (RenderTexture2D):
          LoadRenderTexture() - UnloadRenderTexture()
          BeginTextureMode() - EndTextureMode()
[rlgl] Removed SetShaderMap*() functions
[rlgl] Redesigned default buffers usage functions:
          LoadDefaultBuffers() - UnloadDefaultBuffers()
          UpdateDefaultBuffers() - DrawDefaultBuffers()
[shapes] Corrected bug on GetCollisionRec()
[textures] Added support for Nearest-Neighbor image scaling
[textures] Added functions to draw text on image: ImageDrawText(), ImageDrawTextEx()
[text] Reorganized internal functions: Added LoadImageFont()
[text] Security check for unsupported BMFonts
[models] Split mesh creation from model loading on heightmap and cubicmap
[models] Updated BoundingBox collision detections
[models] Added color parameter to DrawBoundigBox()
[models] Removed function: DrawQuad()
[models] Removed function: SetModelTexture()
[models] Redesigned DrawPlane() to use RL_TRIANGLES
[models] Redesigned DrawRectangleV() to use RL_TRIANGLES
[models] Redesign to accomodate new materials system: LoadMaterial()
[models] Added material functions: LoadDefaultMaterial(), LoadStandardMaterial()
[models] Added MTL material loading support: LoadMTL()
[models] Added function: DrawLight()
[audio] Renamed SoundIsPlaying() to IsSoundPlaying()
[audio] Renamed MusicIsPlaying() to IsMusicPlaying()
[audio] Support multiple Music streams (indexed)
[audio] Support multiple mixing channels
[gestures] Improved and reviewed gestures system
[raymath] Added QuaternionInvert()
[raymath] Removed function: PrintMatrix()
[raygui] Ported to header-only library (https://github.com/raysan5/raygui)
[shaders] Added depth drawing shader (requires a depth texture)
[shaders] Reviewed included shaders and added comments
[OpenAL Soft] Updated to latest version (1.17.2)
[GLFW3] Updated to latest version (3.2)
[stb] Updated to latest headers versions
[GLAD] Converted to header only library and simplified to only used extensions
[*] Reorganize library folders: external libs moved to src/external folder
[*] Reorganize src folder for Android library
[*] Review external dependencies usage
[*] Improved Linux and OSX build systems
[*] Lots of tweaks and bugs corrected all around

-----------------------------------------------
Release:     raylib 1.4.0 (22 February 2016)
-----------------------------------------------
NOTE:
  This version supposed another big improvement for raylib, including new modules and new features.
  More than 30 new functions have been added to previous raylib version.
  Around 8 new examples and +10 new game samples have been added.

BIG changes:
[textures] IMAGE MANIPULATION: Functions to crop, resize, colorize, flip, dither and even draw image-to-image or text-to-image.
[text] SPRITEFONT SUPPORT: Added support for AngelCode fonts (.fnt) and TrueType fonts (.ttf).
[gestures] REDESIGN: Gestures system simplified and prepared to process generic touch events, including mouse events (multiplatform).
[physac] NEW MODULE: Basic 2D physics support, use colliders and rigidbodies; apply forces to physic objects.

other changes:

[rlgl] Removed GLEW library dependency, now using GLAD
[rlgl] Implemented alternative to glGetTexImage() on OpenGL ES
[rlgl] Using depth data on batch drawing
[rlgl] Reviewed glReadPixels() function
[core][rlgl] Reviewed raycast system, now 3D picking works
[core] Android: Reviewed Android App cycle, paused if inactive
[shaders] Implemented Blinn-Phong lighting shading model
[textures] Implemented Floyd-Steinberg dithering - ImageDither()
[text] Added line-break support to DrawText()
[text] Added TrueType Fonts support (using stb_truetype)
[models] Implement function: CalculateBoundingBox(Mesh mesh)
[models] Added functions to check Ray collisions
[models] Improve map resolution control on LoadHeightmap()
[camera] Corrected small-glitch on zoom-in with mouse-wheel
[gestures] Implemented SetGesturesEnabled() to enable only some gestures
[gestures] Implemented GetElapsedTime() on Windows system
[gestures] Support mouse gestures for desktop platforms
[raymath] Complete review of the module and converted to header-only
[easings] Added new module for easing animations
[stb] Updated to latest headers versions
[*] Lots of tweaks around

-----------------------------------------------
Release:     raylib 1.3.0 (01 September 2015)
-----------------------------------------------
NOTE:
  This version supposed a big boost for raylib, new modules have been added with lots of features.
  Most of the modules have been completely reviewed to accomodate to the new features.
  Over 50 new functions have been added to previous raylib version.
  Most of the examples have been redone and +10 new advanced examples have been added.

BIG changes:
[rlgl] SHADERS: Support for model shaders and postprocessing shaders (multiple functions)
[textures] FORMATS: Support for multiple internal formats, including compressed formats
[camera] NEW MODULE: Set of cameras for 3d view: Free, Orbital, 1st person, 3rd person
[gestures] NEW MODULE: Gestures system for Android and HTML5 platforms
[raygui] NEW MODULE: Set of IMGUI elements for tools development (experimental)

other changes:

[rlgl] Added check for OpenGL supported extensions
[rlgl] Added function SetBlenMode() to select some predefined blending modes
[core] Added support for drop&drag of external files into running program
[core] Added functions ShowCursor(), HideCursor(), IsCursorHidden()
[core] Renamed function SetFlags() to SetConfigFlags()
[shapes] Simplified some functions to improve performance
[textures] Review of Image struct to support multiple data formats
[textures] Added function LoadImageEx()
[textures] Added function LoadImageRaw()
[textures] Added function LoadTextureEx()
[textures] Simplified function parameters LoadTextureFromImage()
[textures] Added function GetImageData()
[textures] Added function GetTextureData()
[textures] Renamed function ConvertToPOT() to ImageConvertToPOT()
[textures] Added function ImageConvertFormat()
[textures] Added function GenTextureMipmaps()
[text] Added support for Latin-1 Extended characters for default font
[text] Redesigned SpriteFont struct, replaced Character struct by Rectangle
[text] Removed function GetFontBaseSize(), use directly spriteFont.size
[models] Review of struct: Model (added shaders support)
[models] Added 3d collision functions (sphere vs sphere vs box vs box)
[models] Added function DrawCubeTexture()
[models] Added function DrawQuad()
[models] Added function DrawRay()
[models] Simplified function DrawPlane()
[models] Removed function DrawPlaneEx()
[models] Simplified function DrawGizmo()
[models] Removed function DrawGizmoEx()
[models] Added function LoadModelEx()
[models] Review of function LoadCubicMap()
[models] Added function ResolveCollisionCubicmap()
[audio] Decopupled from raylib, now this module can be used as standalone
[audio] Added function UpdateMusicStream()
[raymath] Complete review of the module
[stb] Updated to latest headers versions
[*] Lots of tweaks around

-----------------------------------------------
Release:     raylib 1.2.2 (31 December 2014)
-----------------------------------------------
[*] Added support for HTML5 compiling (emscripten, asm.js)
[core] Corrected bug on input handling (keyboard and mouse)
[textures] Renamed function CreateTexture() to LoadTextureFromImage()
[textures] Added function ConvertToPOT()
[rlgl] Added support for color tint on models on GL 3.3+ and ES2
[rlgl] Added support for normals on models
[models] Corrected bug on DrawBillboard()
[models] Corrected bug on DrawHeightmap()
[models] Renamed LoadCubesmap() to LoadCubicmap()
[audio] Added function LoadSoundFromWave()
[makefile] Added support for Linux and OSX compiling
[stb] Updated to latest headers versions
[*] Lots of tweaks around

---------------------------------------------------------------
Update:     raylib 1.2.1 (17 October 2014) (Small Fixes Update)
---------------------------------------------------------------
[core] Added function SetupFlags() to preconfigure raylib Window
[core] Corrected bug on fullscreen mode
[rlgl] rlglDrawmodel() - Added rotation on Y axis
[text] MeasureTextEx() - Corrected bug on measures for default font

-----------------------------------------------
Release:     raylib 1.2 (16 September 2014)
-----------------------------------------------
NOTE:
  This version supposed a complete redesign of the [core] module to support Android and Raspberry Pi.
  Multiples modules have also been tweaked to accomodate to the new platforms, specially [rlgl]

[core] Added multiple platforms support: Android and Raspberry Pi
[core] InitWindow() - Complete rewrite and split for Android
[core] InitDisplay() - Internal function added to calculate proper display size
[core] InitGraphics() - Internal function where OpenGL graphics are initialized
[core] Complete refactoring of input functions to accomodate to new platforms
[core] Mouse and Keyboard raw data reading functions added for Raspberry Pi
[core] GetTouchX(), GetTouchY() - Added for Android
[core] Added Android callbacks to process inputs and Android activity commands
[rlgl] Adjusted buffers depending on platform
[rlgl] Added security check in case deployed vertex excess buffer size
[rlgl] Adjusted indices type depending on GL version (int or short)
[rlgl] Fallback to VBOs only usage if VAOs not supported on ES2
[rlgl] rlglLoadModel() stores vbo ids on new Model struct
[textures] Added support for PKM files (ETC1, ETC2 compression support)
[shapes] DrawRectangleV() - Modified, depending on OGL version uses TRIANGLES or QUADS
[text] LoadSpriteFont() - Modified to use LoadImage()
[models] Minor changes on models loading to accomodate to new Model struct
[audio] PauseMusicStream(), ResumeMusicStream() - Added
[audio] Reduced music buffer size to avoid stalls on Raspberry Pi
[src] Added makefile for Windows and RPI
[src] Added resources file (raylib icon and executable info)
[examples] Added makefile for Windows and RPI
[examples] Renamed and merged with test examples for coherence with module names
[templates] Added multiple templates to be use as a base-code for games

-----------------------------------------------
Release:     raylib 1.1.1 (22 July 2014)
-----------------------------------------------
[core] ShowLogo() - To enable raylib logo animation at startup
[core] Corrected bug with window resizing
[rlgl] Redefined colors arrays to use byte instead of float
[rlgl] Removed double buffer system (no performance improvement)
[rlgl] rlglDraw() - Reorganized buffers drawing order
[rlgl] Corrected bug on screen resizing
[shapes] DrawRectangle() - Use QUADS instead of TRIANGLES
[models] DrawSphereWires() - Corrected some issues
[models] LoadOBJ() - Redesigned to support multiple meshes
[models] LoadCubesMap() - Loading a map as cubes (by pixel color)
[textures] Added security check if file doesn't exist
[text] Corrected bug on SpriteFont loading
[examples] Corrected some 3d examples
[test] Added cubesmap loading test

-----------------------------------------------
Release:     raylib 1.1.0 (19 April 2014)
-----------------------------------------------
NOTE:
  This version supposed a complete internal redesign of the library to support OpenGL 3.3+ and OpenGL ES 2.0.
  New module [rlgl] has been added to 'translate' immediate mode style functions (i.e. rlVertex3f()) to GL 1.1, 3.3+ or ES2.
  Another new module [raymath] has also been added with lot of useful 3D math vector-matrix-quaternion functions.

[rlgl] New module, abstracts OpenGL rendering (multiple versions support)
[raymath] New module, useful 3D math vector-matrix-quaternion functions
[core] Adapt all OpenGL code (initialization, drawing) to use [rlgl]
[shapes] Rewrite all shapes drawing functions to use [rlgl]
[textures] Adapt texture GPU loading to use [rlgl]
[textures] Added support for DDS images (compressed and uncompressed)
[textures] CreateTexture() - Redesigned to add mipmap automatic generation
[textures] DrawTexturePro() - Redesigned and corrected bugs
[models] Rewrite all 3d-shapes drawing functions to use [rlgl]
[models] Adapt model loading and drawing to use [rlgl]
[models] Model struct updated to include texture id
[models] SetModelTexture() - Added, link a texture to a model
[models] DrawModelEx() - Redesigned with extended parameters
[audio] Added music streaming support (OGG files)
[audio] Added support for OGG files as Sound
[audio] PlayMusicStream() - Added, open a new music stream and play it
[audio] StopMusicStream() - Added, stop music stream playing and close stream
[audio] PauseMusicStream() - Added, pause music stream playing
[audio] MusicIsPlaying() - Added, to check if music is playing
[audio] SetMusicVolume() - Added, set volume for music
[audio] GetMusicTimeLength() - Added, get current music time length (in seconds)
[audio] GetMusicTimePlayed() - Added, get current music time played (in seconds)
[utils] Added log tracing functionality - TraceLog(), TraceLogOpen(), TraceLogClose()
[*] Log tracing messages all around the code

-----------------------------------------------
Release:     raylib 1.0.6 (16 March 2014)
-----------------------------------------------
[core] Removed unused lighting-system code
[core] Removed SetPerspective() function, calculated directly
[core] Unload and reload default font on fullscreen toggle
[core] Corrected bug gamepad buttons checking if no gamepad available
[texture] DrawTextureV() - Added, to draw using Vector2 for position
[texture] LoadTexture() - Redesigned, now uses LoadImage() + CreateTexture()
[text] FormatText() - Corrected memory leak bug
[models] Added Matrix struct and related functions
[models] DrawBillboard() - Reviewed, now it works!
[models] DrawBillboardRec() - Reviewed, now it works!
[tests] Added folder with multiple tests for new functions

-----------------------------------------------
Update:     raylib 1.0.5 (28 January 2014)
-----------------------------------------------
[audio] LoadSound() - Corrected a bug, WAV file was not closed!
[core] GetMouseWheelMove() - Added, check mouse wheel Y movement
[texture] CreateTexture2D() renamed to CreateTexture()
[models] LoadHeightmap() - Added, Heightmap can be loaded as a Model
[tool] rREM updated, now supports (partially) drag and drop of files

-----------------------------------------------
Release:     raylib 1.0.4 (23 January 2014)
-----------------------------------------------
[tool] Published a first alpha version of rREM tool (raylib Resource Embedder)
[core] GetRandomValue() - Bug corrected, now works right
[core] Fade() - Added, fades a color to an alpha percentadge
[core] WriteBitmap() - Moved to new module: utils.c, not used anymore
[core] TakeScreenshot() - Now uses WritePNG() (utils.c)
[utils] New module created with utility functions
[utils] WritePNG() - Write a PNG file (used by TakeScreenshot() on core)
[utils] DecompressData() - Added, used for rRES resource data decompresion
[textures] LoadImageFromRES() - Added, load an image from a rRES resource file
[textures] LoadTextureFromRES() - Added, load a texture from a rRES resource file
[audio] LoadSoundFromRES() - Added, load a sound from a rRES resource file
[audio] IsPlaying() - Added, check if a sound is currently playing
[audio] SetVolume() - Added, set the volume for a sound
[audio] SetPitch() - Added, set the pitch for a sound
[examples] ex06a_color_select completed
[examples] ex06b_logo_anim completed
[examples] ex06c_font select completed

-----------------------------------------------
Release:     raylib 1.0.3 (19 December 2013)
-----------------------------------------------
[fonts] Added 8 rBMF free fonts to be used on projects!
[text] LoadSpriteFont() - Now supports rBMF file loading (raylib Bitmap Font)
[examples] ex05a_sprite_fonts completed
[examples] ex05b_rbmf_fonts completed
[core] InitWindowEx() - InitWindow with extended parameters, resizing option and custom cursor!
[core] GetRandomValue() - Added, returns a random value within a range (int)
[core] SetExitKey() - Added, sets a key to exit program (default is ESC)
[core] Custom cursor not drawn when mouse out of screen
[shapes] CheckCollisionPointRec() - Added, check collision between point and rectangle
[shapes] CheckCollisionPointCircle() - Added, check collision between point and circle
[shapes] CheckCollisionPointTriangle() - Added, check collision between point and triangle
[shapes] DrawPoly() - Added, draw regular polygons of n sides, rotation can be defined!

-----------------------------------------------
Release:     raylib 1.0.2 (1 December 2013)
-----------------------------------------------
[text] GetDefaultFont() - Added, get default SpriteFont to be used on DrawTextEx()
[shapes] CheckCollisionRecs() - Added, check collision between rectangles
[shapes] CheckCollisionCircles() - Added, check collision between circles
[shapes] CheckCollisionCircleRec() - Added, check collision circle-rectangle
[shapes] GetCollisionRec() - Added, get collision rectangle
[textures] CreateTexture2D() - Added, create Texture2D from Image data
[audio] Fixed WAV loading function, now audio works!

-----------------------------------------------
Update:     raylib 1.0.1 (28 November 2013)
-----------------------------------------------
[text] DrawText() - Removed spacing parameter
[text] MeasureText() - Removed spacing parameter
[text] DrawFps() - Renamed to DrawFPS() for coherence with similar function
[core] IsKeyPressed() - Change functionality, check if key pressed once
[core] IsKeyDown() - Added, check if key is being pressed
[core] IsKeyReleased() - Change functionality, check if key released once
[core] IsKeyUp() - Added, check if key is being NOT pressed
[core] IsMouseButtonDown() - Added, check if mouse button is being pressed
[core] IsMouseButtonPressed() - Change functionality, check if mouse button pressed once
[core] IsMouseButtonUp() - Added, check if mouse button is NOT being pressed
[core] IsMouseButtonReleased() - Change functionality, check if mouse button released once
[textures] DrawTexturePro() - Added, texture drawing with 'pro' parameters
[examples] Function changes applied to ALL examples

-----------------------------------------------
Release:    raylib 1.0.0 (18 November 2013)
-----------------------------------------------
* Initial version
* 6 Modules provided:
    - core:     basic window/context creation functions, input management, timing functions
    - shapes:   basic shapes drawing functions
    - textures: image data loading and conversion to OpenGL textures
    - text:     text drawing, sprite fonts loading, default font loading
    - models:   basic 3d shapes drawing, OBJ models loading and drawing
    - audio:    audio device initialization, WAV files loading and playing
