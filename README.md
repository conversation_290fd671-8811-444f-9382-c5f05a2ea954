# Dexter: Dark Passenger - 3D Investigation Game

A 3D stealth investigation game inspired by the Dexter TV series, built with Go and Raylib.

## Game Description

Play as <PERSON> in a 3D environment where you must:
- Investigate crime scenes and collect evidence
- Avoid detection by NPCs (police, witnesses, etc.)
- Use stealth mechanics to complete your objectives
- Navigate through Miami-inspired 3D environments

## Features

- **3D Graphics**: Built with <PERSON><PERSON><PERSON> for smooth 3D rendering
- **Stealth Gameplay**: Avoid NPC detection with visual detection radii
- **Evidence Collection**: Find and collect various pieces of evidence
- **Dynamic Camera**: Third-person camera that follows the player
- **Real-time Detection**: NPCs alert when player gets too close
- **Atmospheric Environment**: Dark, crime-scene inspired visuals

## Controls

- **WASD**: Move Dexter around the environment
- **E**: Collect evidence when near items
- **ESC**: Exit the game
- **ENTER**: Start the game from main menu

## Installation & Setup

### Prerequisites

1. **Go**: Make sure you have Go installed (version 1.19 or later)
2. **Raylib DLL**: The `raylib.dll` file must be in the same directory as the executable

### Building the Game

1. Clone or download this repository
2. Make sure `raylib.dll` is in the project directory
3. Build the game:
   ```bash
   set CGO_ENABLED=0
   go build -o dexter_game.exe dexter_simple.go
   ```

### Running the Game

Simply run the executable:
```bash
./dexter_game.exe
```

Or run directly with Go:
```bash
set CGO_ENABLED=0
go run dexter_simple.go
```

## Game Mechanics

### Evidence Collection
- Golden cubes represent evidence items
- Walk close to evidence and press **E** to collect
- Evidence counter shows your progress in the top-left corner

### Stealth System
- Orange NPCs patrol the area
- Red circles show their detection radius
- If you enter their detection radius, they turn red (alerted)
- "DETECTED!" appears when you're spotted
- "HIDDEN" shows when you're safe

### Environment
- Dark green ground represents the crime scene area
- Gray buildings provide cover and atmosphere
- Player is represented by a blue cube
- NPCs are orange/red cubes with detection circles

## Technical Details

- **Language**: Go (Golang)
- **Graphics Library**: Raylib-go (Go bindings for Raylib)
- **Rendering**: OpenGL 3.3
- **Platform**: Windows (with raylib.dll)
- **Architecture**: Component-based game architecture

## Game Structure

The game is organized into several key components:

- `Player`: Handles player movement and state
- `Evidence`: Manages collectible items
- `NPC`: Controls non-player character behavior and detection
- `Game`: Main game state and logic coordinator
- `Camera`: 3D camera system that follows the player

## Future Enhancements

This is a basic implementation that could be expanded with:

- More complex AI patrol patterns
- Multiple levels/crime scenes
- Sound effects and music
- More detailed 3D models
- Inventory system
- Mission objectives
- Save/load functionality
- More sophisticated stealth mechanics

## Dexter Theme Integration

The game incorporates elements from the Dexter series:

- **Investigation Focus**: Collecting evidence is the primary objective
- **Stealth Elements**: Avoiding detection mirrors Dexter's careful approach
- **Crime Scene Setting**: The environment represents forensic investigation areas
- **Dark Atmosphere**: Visual style reflects the show's dark themes

## Troubleshooting

### Common Issues

1. **"raylib.dll not found"**: Make sure `raylib.dll` is in the same directory as the executable
2. **Game won't start**: Ensure you have proper graphics drivers installed
3. **Performance issues**: Try reducing the window size or running on a system with better graphics support

### System Requirements

- Windows 10 or later
- OpenGL 3.3 compatible graphics card
- At least 4GB RAM
- 50MB free disk space

## Development

This game was created as a demonstration of:
- 3D game development in Go
- Raylib integration
- Basic game mechanics implementation
- Stealth gameplay systems

The code is structured to be easily extensible for additional features and improvements.

## License

This project is for educational and demonstration purposes. Raylib is licensed under the zlib/libpng license.

---

**Enjoy investigating crime scenes as Dexter Morgan!** 🔍🎮
